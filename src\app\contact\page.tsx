'use client'

import { useState } from 'react'
import PageWrapper from '@/components/layout/PageWrapper'
import Circle from '@/components/shapes/Circle'
import Rectangle from '@/components/shapes/Rectangle'
import Triangle from '@/components/shapes/Triangle'
import HalfCircle from '@/components/shapes/HalfCircle'

export default function Contact() {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    message: ''
  })

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // Handle form submission here
    console.log('Form submitted:', formData)
    // Reset form
    setFormData({ name: '', email: '', message: '' })
  }

  return (
    <PageWrapper>
      <section className="relative px-6 md:px-12 lg:px-24 py-16 md:py-24 overflow-hidden">
        <div className="max-w-6xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-start">
            {/* Form Section */}
            <div>
              <h1 className="text-display font-bold mb-8">
                Have something worth building? Let's talk.
              </h1>
              
              <form onSubmit={handleSubmit} className="space-y-8">
                <div>
                  <label htmlFor="name" className="block text-sm font-bold uppercase tracking-wide mb-3">
                    Name
                  </label>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleChange}
                    required
                    className="w-full px-4 py-4 border-2 border-bauhaus-black bg-brand-background text-bauhaus-black focus:outline-none focus:border-bauhaus-red transition-colors duration-200"
                  />
                </div>
                
                <div>
                  <label htmlFor="email" className="block text-sm font-bold uppercase tracking-wide mb-3">
                    Email
                  </label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    value={formData.email}
                    onChange={handleChange}
                    required
                    className="w-full px-4 py-4 border-2 border-bauhaus-black bg-brand-background text-bauhaus-black focus:outline-none focus:border-bauhaus-red transition-colors duration-200"
                  />
                </div>
                
                <div>
                  <label htmlFor="message" className="block text-sm font-bold uppercase tracking-wide mb-3">
                    Message
                  </label>
                  <textarea
                    id="message"
                    name="message"
                    value={formData.message}
                    onChange={handleChange}
                    required
                    rows={6}
                    className="w-full px-4 py-4 border-2 border-bauhaus-black bg-brand-background text-bauhaus-black focus:outline-none focus:border-bauhaus-red transition-colors duration-200 resize-none"
                  />
                </div>
                
                <button type="submit" className="btn-red">
                  Send It
                </button>
              </form>
            </div>
            
            {/* Geometric Composition */}
            <div className="relative h-96 lg:h-[600px]">
              {/* Abstract shape collage */}
              <Circle 
                size="xl" 
                color="red" 
                className="absolute top-12 right-16 opacity-80" 
              />
              <Rectangle 
                width="lg" 
                height="xl" 
                color="blue" 
                className="absolute top-32 left-12 rotate-12 opacity-90" 
              />
              <Triangle 
                size="xl" 
                color="yellow" 
                direction="up" 
                className="absolute bottom-24 right-8 opacity-85" 
              />
              <HalfCircle 
                size="lg" 
                color="black" 
                direction="left" 
                className="absolute top-8 left-32 rotate-45" 
              />
              <Circle 
                size="lg" 
                color="yellow" 
                className="absolute bottom-8 left-8 opacity-70" 
              />
              <Rectangle 
                width="md" 
                height="sm" 
                color="red" 
                className="absolute top-48 right-32 -rotate-45 opacity-75" 
              />
              <Triangle 
                size="md" 
                color="blue" 
                direction="down" 
                className="absolute bottom-32 left-24 opacity-80" 
              />
              
              {/* Faint background lines */}
              <div className="absolute inset-0 opacity-10">
                <div className="w-full h-px bg-bauhaus-black absolute top-1/4"></div>
                <div className="w-full h-px bg-bauhaus-black absolute top-1/2"></div>
                <div className="w-full h-px bg-bauhaus-black absolute top-3/4"></div>
                <div className="w-px h-full bg-bauhaus-black absolute left-1/4"></div>
                <div className="w-px h-full bg-bauhaus-black absolute left-1/2"></div>
                <div className="w-px h-full bg-bauhaus-black absolute left-3/4"></div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </PageWrapper>
  )
}
