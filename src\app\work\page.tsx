import PageWrapper from '@/components/layout/PageWrapper'
import Circle, { SoftCircle } from '@/components/shapes/Circle'
import Rectangle, { RoundedRectangle } from '@/components/shapes/Rectangle'
import Triangle from '@/components/shapes/Triangle'
import HalfCircle from '@/components/shapes/HalfCircle'
import OrganicComposition from '@/components/compositions/OrganicComposition'

export default function Work() {
  const projects = [
    {
      name: "X Platform Redesign",
      summary: "Speed-focused UI for a growing SaaS",
      timeline: "3 weeks",
      outcome: "2x conversions", 
      tech: "React + Tailwind",
      color: "red" as const,
      shapes: [
        { type: "circle", size: "lg", color: "white" },
        { type: "rectangle", width: "md", height: "sm", color: "black" }
      ]
    },
    {
      name: "Fintech Mobile App",
      summary: "Clean interface for complex financial data",
      timeline: "6 weeks",
      outcome: "40% faster tasks",
      tech: "React Native + TypeScript", 
      color: "blue" as const,
      shapes: [
        { type: "triangle", size: "lg", color: "white", direction: "up" },
        { type: "circle", size: "md", color: "yellow" }
      ]
    },
    {
      name: "E-commerce Rebuild",
      summary: "Modern storefront with zero downtime migration",
      timeline: "8 weeks", 
      outcome: "3x page speed",
      tech: "Next.js + Shopify",
      color: "yellow" as const,
      shapes: [
        { type: "halfcircle", size: "lg", color: "black", direction: "top" },
        { type: "rectangle", width: "sm", height: "lg", color: "red" }
      ]
    },
    {
      name: "Healthcare Dashboard",
      summary: "Data visualization for medical professionals",
      timeline: "5 weeks",
      outcome: "50% time saved",
      tech: "Vue.js + D3.js",
      color: "red" as const,
      shapes: [
        { type: "circle", size: "xl", color: "blue" },
        { type: "triangle", size: "md", color: "white", direction: "down" }
      ]
    },
    {
      name: "Startup Landing Page",
      summary: "High-converting page for Series A fundraising",
      timeline: "2 weeks",
      outcome: "$2M raised",
      tech: "Gatsby + Netlify",
      color: "blue" as const,
      shapes: [
        { type: "rectangle", width: "lg", height: "lg", color: "yellow" },
        { type: "circle", size: "sm", color: "black" }
      ]
    },
    {
      name: "SaaS Product Redesign", 
      summary: "Complete UX overhaul for B2B platform",
      timeline: "12 weeks",
      outcome: "60% less support tickets",
      tech: "React + Node.js",
      color: "yellow" as const,
      shapes: [
        { type: "halfcircle", size: "xl", color: "red", direction: "bottom" },
        { type: "triangle", size: "sm", color: "blue", direction: "right" }
      ]
    }
  ]

  const renderShape = (shape: any, index: number) => {
    const key = `shape-${index}`
    const className = index === 0 ? "absolute top-4 left-4" : "absolute bottom-4 right-4"
    
    switch (shape.type) {
      case "circle":
        return <Circle key={key} size={shape.size} color={shape.color} className={className} />
      case "rectangle":
        return <Rectangle key={key} width={shape.width} height={shape.height} color={shape.color} className={className} />
      case "triangle":
        return <Triangle key={key} size={shape.size} color={shape.color} direction={shape.direction} className={className} />
      case "halfcircle":
        return <HalfCircle key={key} size={shape.size} color={shape.color} direction={shape.direction} className={className} />
      default:
        return null
    }
  }

  return (
    <PageWrapper>
      {/* Header Section */}
      <section className="relative px-6 md:px-12 lg:px-24 py-16 md:py-24 overflow-hidden">
        <div className="max-w-4xl mx-auto text-center relative z-10">
          <h1 className="text-display font-bold mb-8">Projects That Speak for Themselves</h1>
        </div>

        {/* Background organic composition */}
        <div className="absolute inset-0 opacity-30">
          <OrganicComposition variant="minimal" className="w-full h-full" />
        </div>
      </section>

      {/* Projects Grid */}
      <section className="px-6 md:px-12 lg:px-24 pb-16 md:pb-24">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {projects.map((project, index) => (
              <div 
                key={index}
                className={`relative p-8 border-2 border-bauhaus-black bg-bauhaus-${project.color} min-h-[300px] flex flex-col justify-between overflow-hidden`}
              >
                {/* Geometric shapes */}
                {project.shapes.map((shape, shapeIndex) => renderShape(shape, shapeIndex))}
                
                {/* Project content */}
                <div className="relative z-10">
                  <h3 className="text-heading font-bold mb-3 text-bauhaus-black">
                    {project.name}
                  </h3>
                  <p className="text-body mb-6 text-bauhaus-black">
                    {project.summary}
                  </p>
                </div>
                
                <div className="relative z-10 space-y-2">
                  <div className="text-sm font-medium text-bauhaus-black">
                    <span className="opacity-75">Timeline:</span> {project.timeline}
                  </div>
                  <div className="text-sm font-medium text-bauhaus-black">
                    <span className="opacity-75">Outcome:</span> {project.outcome}
                  </div>
                  <div className="text-sm font-medium text-bauhaus-black">
                    <span className="opacity-75">Tech:</span> {project.tech}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>
    </PageWrapper>
  )
}
