"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/hooks/useScrollAnimation.ts":
/*!*****************************************!*\
  !*** ./src/hooks/useScrollAnimation.ts ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   animationPresets: function() { return /* binding */ animationPresets; },\n/* harmony export */   useScrollAnimation: function() { return /* binding */ useScrollAnimation; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ useScrollAnimation,animationPresets auto */ \nfunction useScrollAnimation(config) {\n    const [scrollY, setScrollY] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(0);\n    const elementRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const [randomFactors, setRandomFactors] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        x: 0,\n        y: 0\n    });\n    // Generate consistent random factors based on seed with more varied directions\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        const seed = config.randomSeed || Math.random();\n        // Use multiple different seeds to create more varied movement patterns\n        const xSeed = seed * 12.9898;\n        const ySeed = seed * 78.233;\n        const angleSeed = seed * 45.164;\n        // Generate base random values\n        const baseX = Math.sin(xSeed) * 43758.5453 % 1;\n        const baseY = Math.sin(ySeed) * 43758.5453 % 1;\n        const angle = Math.sin(angleSeed) * 43758.5453 % 1;\n        // Create more dynamic directional patterns\n        // Use angle to create diagonal movements and varied directions\n        const angleRad = angle * Math.PI * 2 // Full circle\n        ;\n        const magnitude = 0.8 + Math.abs(baseX) * 0.4 // Vary magnitude between 0.8-1.2\n        ;\n        setRandomFactors({\n            x: Math.cos(angleRad) * magnitude,\n            y: Math.sin(angleRad) * magnitude\n        });\n    }, [\n        config.randomSeed\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        const handleScroll = ()=>{\n            setScrollY(window.scrollY);\n        };\n        // Use passive listener for better performance\n        window.addEventListener(\"scroll\", handleScroll, {\n            passive: true\n        });\n        return ()=>{\n            window.removeEventListener(\"scroll\", handleScroll);\n        };\n    }, []);\n    // Calculate transform values\n    const getTransform = ()=>{\n        if (!elementRef.current) return \"translate3d(0, 0, 0)\";\n        const rect = elementRef.current.getBoundingClientRect();\n        const elementTop = rect.top + scrollY;\n        const scrollProgress = (scrollY - elementTop + window.innerHeight) / (window.innerHeight + rect.height);\n        // Smooth easing function\n        const easeInOutQuad = (t)=>{\n            return t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t;\n        };\n        const easedProgress = easeInOutQuad(Math.max(0, Math.min(1, scrollProgress)));\n        let translateX = 0;\n        let translateY = 0;\n        if (config.direction === \"x\" || config.direction === \"both\") {\n            translateX = easedProgress * config.intensity * randomFactors.x * config.speed;\n        }\n        if (config.direction === \"y\" || config.direction === \"both\") {\n            translateY = easedProgress * config.intensity * randomFactors.y * config.speed;\n        }\n        return \"translate3d(\".concat(translateX, \"px, \").concat(translateY, \"px, 0)\");\n    };\n    return {\n        ref: elementRef,\n        style: {\n            transform: getTransform(),\n            transition: \"transform 0.1s ease-out\",\n            willChange: \"transform\"\n        }\n    };\n}\n// Predefined animation presets for different shape types with varied movement\nconst animationPresets = {\n    subtle: {\n        direction: \"both\",\n        intensity: 25,\n        speed: 0.6\n    },\n    gentle: {\n        direction: \"both\",\n        intensity: 35,\n        speed: 0.8\n    },\n    dynamic: {\n        direction: \"both\",\n        intensity: 55,\n        speed: 1.2\n    },\n    flowing: {\n        direction: \"both\",\n        intensity: 40,\n        speed: 0.9\n    },\n    energetic: {\n        direction: \"both\",\n        intensity: 60,\n        speed: 1.4\n    },\n    drift: {\n        direction: \"both\",\n        intensity: 30,\n        speed: 0.5\n    },\n    pulse: {\n        direction: \"both\",\n        intensity: 45,\n        speed: 1.1\n    },\n    float: {\n        direction: \"both\",\n        intensity: 20,\n        speed: 0.4\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useScrollAnimation.ts\n"));

/***/ })

});