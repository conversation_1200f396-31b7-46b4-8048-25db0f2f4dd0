"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/hooks/useScrollAnimation.ts":
/*!*****************************************!*\
  !*** ./src/hooks/useScrollAnimation.ts ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   animationPresets: function() { return /* binding */ animationPresets; },\n/* harmony export */   useScrollAnimation: function() { return /* binding */ useScrollAnimation; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ useScrollAnimation,animationPresets auto */ \nfunction useScrollAnimation(config) {\n    const [scrollY, setScrollY] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(0);\n    const elementRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const [randomFactors, setRandomFactors] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        x: 0,\n        y: 0\n    });\n    // Generate consistent random factors based on seed with more varied directions\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        const seed = config.randomSeed || Math.random();\n        // Use multiple different seeds to create more varied movement patterns\n        const xSeed = seed * 12.9898;\n        const ySeed = seed * 78.233;\n        const angleSeed = seed * 45.164;\n        // Generate base random values\n        const baseX = Math.sin(xSeed) * 43758.5453 % 1;\n        const baseY = Math.sin(ySeed) * 43758.5453 % 1;\n        const angle = Math.sin(angleSeed) * 43758.5453 % 1;\n        // Create more dynamic directional patterns\n        // Use angle to create diagonal movements and varied directions\n        const angleRad = angle * Math.PI * 2 // Full circle\n        ;\n        const magnitude = 0.8 + Math.abs(baseX) * 0.4 // Vary magnitude between 0.8-1.2\n        ;\n        setRandomFactors({\n            x: Math.cos(angleRad) * magnitude,\n            y: Math.sin(angleRad) * magnitude\n        });\n    }, [\n        config.randomSeed\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        const handleScroll = ()=>{\n            setScrollY(window.scrollY);\n        };\n        // Use passive listener for better performance\n        window.addEventListener(\"scroll\", handleScroll, {\n            passive: true\n        });\n        return ()=>{\n            window.removeEventListener(\"scroll\", handleScroll);\n        };\n    }, []);\n    // Calculate transform values\n    const getTransform = ()=>{\n        if (!elementRef.current) return \"translate3d(0, 0, 0)\";\n        const rect = elementRef.current.getBoundingClientRect();\n        const elementTop = rect.top + scrollY;\n        const scrollProgress = (scrollY - elementTop + window.innerHeight) / (window.innerHeight + rect.height);\n        // Smooth easing function\n        const easeInOutQuad = (t)=>{\n            return t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t;\n        };\n        const easedProgress = easeInOutQuad(Math.max(0, Math.min(1, scrollProgress)));\n        let translateX = 0;\n        let translateY = 0;\n        if (config.direction === \"x\" || config.direction === \"both\") {\n            translateX = easedProgress * config.intensity * randomFactors.x * config.speed;\n        }\n        if (config.direction === \"y\" || config.direction === \"both\") {\n            translateY = easedProgress * config.intensity * randomFactors.y * config.speed;\n        }\n        return \"translate3d(\".concat(translateX, \"px, \").concat(translateY, \"px, 0)\");\n    };\n    return {\n        ref: elementRef,\n        style: {\n            transform: getTransform(),\n            transition: \"transform 0.1s ease-out\",\n            willChange: \"transform\"\n        }\n    };\n}\n// Predefined animation presets for different shape types\nconst animationPresets = {\n    subtle: {\n        direction: \"both\",\n        intensity: 20,\n        speed: 0.5\n    },\n    gentle: {\n        direction: \"both\",\n        intensity: 30,\n        speed: 0.7\n    },\n    dynamic: {\n        direction: \"both\",\n        intensity: 50,\n        speed: 1\n    },\n    horizontal: {\n        direction: \"x\",\n        intensity: 40,\n        speed: 0.8\n    },\n    vertical: {\n        direction: \"y\",\n        intensity: 35,\n        speed: 0.6\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useScrollAnimation.ts\n"));

/***/ })

});