/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CSaud%5CProjects%5Cnavhaus%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CSaud%5CProjects%5Cnavhaus&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CSaud%5CProjects%5Cnavhaus%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CSaud%5CProjects%5Cnavhaus&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CSaud%5CProjects%5Cnavhaus%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CSaud%5CProjects%5Cnavhaus&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CSaud%5CProjects%5Cnavhaus%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CSaud%5CProjects%5Cnavhaus%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CSaud%5CProjects%5Cnavhaus%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CSaud%5CProjects%5Cnavhaus%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CSaud%5CProjects%5Cnavhaus%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CSaud%5CProjects%5Cnavhaus%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CSaud%5CProjects%5Cnavhaus%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CSaud%5CProjects%5Cnavhaus%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CSaud%5CProjects%5Cnavhaus%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CSaud%5CProjects%5Cnavhaus%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CSaud%5CProjects%5Cnavhaus%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CSaud%5CProjects%5Cnavhaus%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CSaud%5CProjects%5Cnavhaus%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CSaud%5CProjects%5Cnavhaus%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CSaud%5CProjects%5Cnavhaus%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CSaud%5CProjects%5Cnavhaus%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CSaud%5CProjects%5Cnavhaus%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CSaud%5CProjects%5Cnavhaus%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CSaud%5CProjects%5Cnavhaus%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Cimage-component.js&modules=C%3A%5CUsers%5CSaud%5CProjects%5Cnavhaus%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Clink.js&modules=C%3A%5CUsers%5CSaud%5CProjects%5Cnavhaus%5Csrc%5Ccomponents%5Clayout%5CHeader.tsx&modules=C%3A%5CUsers%5CSaud%5CProjects%5Cnavhaus%5Csrc%5Ccomponents%5Cshapes%5CAnimatedShapes.tsx&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CSaud%5CProjects%5Cnavhaus%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Cimage-component.js&modules=C%3A%5CUsers%5CSaud%5CProjects%5Cnavhaus%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Clink.js&modules=C%3A%5CUsers%5CSaud%5CProjects%5Cnavhaus%5Csrc%5Ccomponents%5Clayout%5CHeader.tsx&modules=C%3A%5CUsers%5CSaud%5CProjects%5Cnavhaus%5Csrc%5Ccomponents%5Cshapes%5CAnimatedShapes.tsx&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/image-component.js */ \"(ssr)/./node_modules/next/dist/client/image-component.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/link.js */ \"(ssr)/./node_modules/next/dist/client/link.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/layout/Header.tsx */ \"(ssr)/./src/components/layout/Header.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/shapes/AnimatedShapes.tsx */ \"(ssr)/./src/components/shapes/AnimatedShapes.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDU2F1ZCU1Q1Byb2plY3RzJTVDbmF2aGF1cyU1Q25vZGVfbW9kdWxlcyU1Q25leHQlNUNkaXN0JTVDY2xpZW50JTVDaW1hZ2UtY29tcG9uZW50LmpzJm1vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDU2F1ZCU1Q1Byb2plY3RzJTVDbmF2aGF1cyU1Q25vZGVfbW9kdWxlcyU1Q25leHQlNUNkaXN0JTVDY2xpZW50JTVDbGluay5qcyZtb2R1bGVzPUMlM0ElNUNVc2VycyU1Q1NhdWQlNUNQcm9qZWN0cyU1Q25hdmhhdXMlNUNzcmMlNUNjb21wb25lbnRzJTVDbGF5b3V0JTVDSGVhZGVyLnRzeCZtb2R1bGVzPUMlM0ElNUNVc2VycyU1Q1NhdWQlNUNQcm9qZWN0cyU1Q25hdmhhdXMlNUNzcmMlNUNjb21wb25lbnRzJTVDc2hhcGVzJTVDQW5pbWF0ZWRTaGFwZXMudHN4JnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxzTkFBNkg7QUFDN0gsZ01BQWtIO0FBQ2xILGdMQUE0RztBQUM1RyIsInNvdXJjZXMiOlsid2VicGFjazovL25hdmhhdXMtd2Vic2l0ZS8/MDljZSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXFNhdWRcXFxcUHJvamVjdHNcXFxcbmF2aGF1c1xcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxpbWFnZS1jb21wb25lbnQuanNcIik7XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXFNhdWRcXFxcUHJvamVjdHNcXFxcbmF2aGF1c1xcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxsaW5rLmpzXCIpO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxTYXVkXFxcXFByb2plY3RzXFxcXG5hdmhhdXNcXFxcc3JjXFxcXGNvbXBvbmVudHNcXFxcbGF5b3V0XFxcXEhlYWRlci50c3hcIik7XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXFNhdWRcXFxcUHJvamVjdHNcXFxcbmF2aGF1c1xcXFxzcmNcXFxcY29tcG9uZW50c1xcXFxzaGFwZXNcXFxcQW5pbWF0ZWRTaGFwZXMudHN4XCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CSaud%5CProjects%5Cnavhaus%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Cimage-component.js&modules=C%3A%5CUsers%5CSaud%5CProjects%5Cnavhaus%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Clink.js&modules=C%3A%5CUsers%5CSaud%5CProjects%5Cnavhaus%5Csrc%5Ccomponents%5Clayout%5CHeader.tsx&modules=C%3A%5CUsers%5CSaud%5CProjects%5Cnavhaus%5Csrc%5Ccomponents%5Cshapes%5CAnimatedShapes.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CSaud%5CProjects%5Cnavhaus%5Csrc%5Capp%5Cglobals.css&server=true!":
/*!*********************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CSaud%5CProjects%5Cnavhaus%5Csrc%5Capp%5Cglobals.css&server=true! ***!
  \*********************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./src/components/layout/Header.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Header.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_4__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction Header() {\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.usePathname)();\n    const navItems = [\n        {\n            href: \"/\",\n            label: \"Home\"\n        },\n        {\n            href: \"/about\",\n            label: \"About\"\n        },\n        {\n            href: \"/work\",\n            label: \"Work\"\n        },\n        {\n            href: \"/contact\",\n            label: \"Contact\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"w-full py-6 px-6 md:px-12 lg:px-24\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                    className: \"flex justify-between items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/\",\n                            className: \"flex items-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                src: \"/images/logo.png\",\n                                alt: \"Navhaus\",\n                                width: 120,\n                                height: 40,\n                                className: \"h-8 w-auto -mt-[15px]\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 25,\n                                columnNumber: 11\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 24,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden md:flex items-center space-x-8\",\n                            children: [\n                                navItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: item.href,\n                                        className: `font-medium uppercase tracking-wide transition-colors duration-200 ${pathname === item.href ? \"text-bauhaus-red\" : \"text-bauhaus-black hover:text-bauhaus-red\"}`,\n                                        children: item.label\n                                    }, item.href, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 37,\n                                        columnNumber: 13\n                                    }, this)),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/contact\",\n                                    className: \"btn-primary ml-8\",\n                                    children: \"Start Project\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 51,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 35,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"md:hidden text-bauhaus-black\",\n                            onClick: ()=>setIsMenuOpen(!isMenuOpen),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-6 h-6\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: isMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M6 18L18 6M6 6l12 12\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 63,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M4 6h16M4 12h16M4 18h16\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 65,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 61,\n                                columnNumber: 11\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 57,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                    lineNumber: 22,\n                    columnNumber: 9\n                }, this),\n                isMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"md:hidden mt-6 py-6 border-t border-bauhaus-black\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col space-y-4\",\n                        children: [\n                            navItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: item.href,\n                                    className: `font-medium uppercase tracking-wide transition-colors duration-200 ${pathname === item.href ? \"text-bauhaus-red\" : \"text-bauhaus-black hover:text-bauhaus-red\"}`,\n                                    onClick: ()=>setIsMenuOpen(false),\n                                    children: item.label\n                                }, item.href, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 76,\n                                    columnNumber: 17\n                                }, this)),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/contact\",\n                                className: \"btn-primary inline-block mt-4\",\n                                onClick: ()=>setIsMenuOpen(false),\n                                children: \"Start Project\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 89,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                        lineNumber: 74,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                    lineNumber: 73,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n            lineNumber: 21,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/Header.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/shapes/AnimatedShapes.tsx":
/*!**************************************************!*\
  !*** ./src/components/shapes/AnimatedShapes.tsx ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AnimatedBlob: () => (/* binding */ AnimatedBlob),\n/* harmony export */   AnimatedCircle: () => (/* binding */ AnimatedCircle),\n/* harmony export */   AnimatedHalfCircle: () => (/* binding */ AnimatedHalfCircle),\n/* harmony export */   AnimatedPill: () => (/* binding */ AnimatedPill),\n/* harmony export */   AnimatedQuarterCircle: () => (/* binding */ AnimatedQuarterCircle),\n/* harmony export */   AnimatedRectangle: () => (/* binding */ AnimatedRectangle),\n/* harmony export */   AnimatedRoundedRect: () => (/* binding */ AnimatedRoundedRect),\n/* harmony export */   AnimatedRoundedRectangle: () => (/* binding */ AnimatedRoundedRectangle),\n/* harmony export */   AnimatedSoftCircle: () => (/* binding */ AnimatedSoftCircle),\n/* harmony export */   AnimatedSoftGrid: () => (/* binding */ AnimatedSoftGrid),\n/* harmony export */   AnimatedTriangle: () => (/* binding */ AnimatedTriangle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _hooks_useScrollAnimation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/hooks/useScrollAnimation */ \"(ssr)/./src/hooks/useScrollAnimation.ts\");\n/* harmony import */ var _Circle__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Circle */ \"(ssr)/./src/components/shapes/Circle.tsx\");\n/* harmony import */ var _Rectangle__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Rectangle */ \"(ssr)/./src/components/shapes/Rectangle.tsx\");\n/* harmony import */ var _Triangle__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./Triangle */ \"(ssr)/./src/components/shapes/Triangle.tsx\");\n/* harmony import */ var _HalfCircle__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./HalfCircle */ \"(ssr)/./src/components/shapes/HalfCircle.tsx\");\n/* harmony import */ var _RoundedShapes__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./RoundedShapes */ \"(ssr)/./src/components/shapes/RoundedShapes.tsx\");\n/* __next_internal_client_entry_do_not_use__ AnimatedCircle,AnimatedSoftCircle,AnimatedRectangle,AnimatedRoundedRectangle,AnimatedTriangle,AnimatedHalfCircle,AnimatedSoftGrid,AnimatedRoundedRect,AnimatedPill,AnimatedBlob,AnimatedQuarterCircle auto */ \n\n\n\n\n\n\n// Generate a consistent random seed based on position and type\nconst generateSeed = (type, index = 0)=>{\n    let hash = 0;\n    const str = `${type}-${index}`;\n    for(let i = 0; i < str.length; i++){\n        const char = str.charCodeAt(i);\n        hash = (hash << 5) - hash + char;\n        hash = hash & hash // Convert to 32-bit integer\n        ;\n    }\n    return Math.abs(hash) / 2147483647 // Normalize to 0-1\n    ;\n};\nfunction AnimatedCircle({ animationPreset = \"gentle\", animationIndex = 0, ...props }) {\n    const animation = (0,_hooks_useScrollAnimation__WEBPACK_IMPORTED_MODULE_1__.useScrollAnimation)({\n        ..._hooks_useScrollAnimation__WEBPACK_IMPORTED_MODULE_1__.animationPresets[animationPreset],\n        randomSeed: generateSeed(\"circle\", animationIndex)\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: animation.ref,\n        className: \"h-full\",\n        style: animation.style,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Circle__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            ...props\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\AnimatedShapes.tsx\",\n            lineNumber: 43,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\AnimatedShapes.tsx\",\n        lineNumber: 42,\n        columnNumber: 5\n    }, this);\n}\nfunction AnimatedSoftCircle({ animationPreset = \"subtle\", animationIndex = 0, ...props }) {\n    const animation = (0,_hooks_useScrollAnimation__WEBPACK_IMPORTED_MODULE_1__.useScrollAnimation)({\n        ..._hooks_useScrollAnimation__WEBPACK_IMPORTED_MODULE_1__.animationPresets[animationPreset],\n        randomSeed: generateSeed(\"soft-circle\", animationIndex)\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: animation.ref,\n        className: \"h-full\",\n        style: animation.style,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Circle__WEBPACK_IMPORTED_MODULE_2__.SoftCircle, {\n            ...props\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\AnimatedShapes.tsx\",\n            lineNumber: 60,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\AnimatedShapes.tsx\",\n        lineNumber: 59,\n        columnNumber: 5\n    }, this);\n}\nfunction AnimatedRectangle({ animationPreset = \"gentle\", animationIndex = 0, ...props }) {\n    const animation = (0,_hooks_useScrollAnimation__WEBPACK_IMPORTED_MODULE_1__.useScrollAnimation)({\n        ..._hooks_useScrollAnimation__WEBPACK_IMPORTED_MODULE_1__.animationPresets[animationPreset],\n        randomSeed: generateSeed(\"rectangle\", animationIndex)\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: animation.ref,\n        className: \"h-full\",\n        style: animation.style,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Rectangle__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            ...props\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\AnimatedShapes.tsx\",\n            lineNumber: 87,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\AnimatedShapes.tsx\",\n        lineNumber: 86,\n        columnNumber: 5\n    }, this);\n}\nfunction AnimatedRoundedRectangle({ animationPreset = \"gentle\", animationIndex = 0, ...props }) {\n    const animation = (0,_hooks_useScrollAnimation__WEBPACK_IMPORTED_MODULE_1__.useScrollAnimation)({\n        ..._hooks_useScrollAnimation__WEBPACK_IMPORTED_MODULE_1__.animationPresets[animationPreset],\n        randomSeed: generateSeed(\"rounded-rectangle\", animationIndex)\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: animation.ref,\n        className: \"h-full\",\n        style: animation.style,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Rectangle__WEBPACK_IMPORTED_MODULE_3__.RoundedRectangle, {\n            ...props\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\AnimatedShapes.tsx\",\n            lineNumber: 104,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\AnimatedShapes.tsx\",\n        lineNumber: 103,\n        columnNumber: 5\n    }, this);\n}\nfunction AnimatedTriangle({ animationPreset = \"dynamic\", animationIndex = 0, ...props }) {\n    const animation = (0,_hooks_useScrollAnimation__WEBPACK_IMPORTED_MODULE_1__.useScrollAnimation)({\n        ..._hooks_useScrollAnimation__WEBPACK_IMPORTED_MODULE_1__.animationPresets[animationPreset],\n        randomSeed: generateSeed(\"triangle\", animationIndex)\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: animation.ref,\n        className: \"h-full\",\n        style: animation.style,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Triangle__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            ...props\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\AnimatedShapes.tsx\",\n            lineNumber: 131,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\AnimatedShapes.tsx\",\n        lineNumber: 130,\n        columnNumber: 5\n    }, this);\n}\nfunction AnimatedHalfCircle({ animationPreset = \"gentle\", animationIndex = 0, ...props }) {\n    const animation = (0,_hooks_useScrollAnimation__WEBPACK_IMPORTED_MODULE_1__.useScrollAnimation)({\n        ..._hooks_useScrollAnimation__WEBPACK_IMPORTED_MODULE_1__.animationPresets[animationPreset],\n        randomSeed: generateSeed(\"half-circle\", animationIndex)\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: animation.ref,\n        className: \"h-full\",\n        style: animation.style,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_HalfCircle__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            ...props\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\AnimatedShapes.tsx\",\n            lineNumber: 158,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\AnimatedShapes.tsx\",\n        lineNumber: 157,\n        columnNumber: 5\n    }, this);\n}\nfunction AnimatedSoftGrid({ animationPreset = \"subtle\", animationIndex = 0, opacity = \"default\", ...props }) {\n    const animation = (0,_hooks_useScrollAnimation__WEBPACK_IMPORTED_MODULE_1__.useScrollAnimation)({\n        ..._hooks_useScrollAnimation__WEBPACK_IMPORTED_MODULE_1__.animationPresets[animationPreset],\n        randomSeed: generateSeed(\"soft-grid\", animationIndex)\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: animation.ref,\n        className: \"h-full\",\n        style: animation.style,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_RoundedShapes__WEBPACK_IMPORTED_MODULE_6__.SoftGrid, {\n            opacity: opacity,\n            ...props\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\AnimatedShapes.tsx\",\n            lineNumber: 188,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\AnimatedShapes.tsx\",\n        lineNumber: 187,\n        columnNumber: 5\n    }, this);\n}\nfunction AnimatedRoundedRect({ animationPreset = \"gentle\", animationIndex = 0, ...props }) {\n    const animation = (0,_hooks_useScrollAnimation__WEBPACK_IMPORTED_MODULE_1__.useScrollAnimation)({\n        ..._hooks_useScrollAnimation__WEBPACK_IMPORTED_MODULE_1__.animationPresets[animationPreset],\n        randomSeed: generateSeed(\"rounded-rect\", animationIndex)\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: animation.ref,\n        className: \"h-full\",\n        style: animation.style,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_RoundedShapes__WEBPACK_IMPORTED_MODULE_6__.RoundedRect, {\n            ...props\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\AnimatedShapes.tsx\",\n            lineNumber: 205,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\AnimatedShapes.tsx\",\n        lineNumber: 204,\n        columnNumber: 5\n    }, this);\n}\nfunction AnimatedPill({ animationPreset = \"horizontal\", animationIndex = 0, ...props }) {\n    const animation = (0,_hooks_useScrollAnimation__WEBPACK_IMPORTED_MODULE_1__.useScrollAnimation)({\n        ..._hooks_useScrollAnimation__WEBPACK_IMPORTED_MODULE_1__.animationPresets[animationPreset],\n        randomSeed: generateSeed(\"pill\", animationIndex)\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: animation.ref,\n        className: \"h-full\",\n        style: animation.style,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_RoundedShapes__WEBPACK_IMPORTED_MODULE_6__.Pill, {\n            ...props\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\AnimatedShapes.tsx\",\n            lineNumber: 222,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\AnimatedShapes.tsx\",\n        lineNumber: 221,\n        columnNumber: 5\n    }, this);\n}\nfunction AnimatedBlob({ animationPreset = \"dynamic\", animationIndex = 0, ...props }) {\n    const animation = (0,_hooks_useScrollAnimation__WEBPACK_IMPORTED_MODULE_1__.useScrollAnimation)({\n        ..._hooks_useScrollAnimation__WEBPACK_IMPORTED_MODULE_1__.animationPresets[animationPreset],\n        randomSeed: generateSeed(\"blob\", animationIndex)\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: animation.ref,\n        className: \"h-full\",\n        style: animation.style,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_RoundedShapes__WEBPACK_IMPORTED_MODULE_6__.Blob, {\n            ...props\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\AnimatedShapes.tsx\",\n            lineNumber: 239,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\AnimatedShapes.tsx\",\n        lineNumber: 238,\n        columnNumber: 5\n    }, this);\n}\nfunction AnimatedQuarterCircle({ animationPreset = \"gentle\", animationIndex = 0, ...props }) {\n    const animation = (0,_hooks_useScrollAnimation__WEBPACK_IMPORTED_MODULE_1__.useScrollAnimation)({\n        ..._hooks_useScrollAnimation__WEBPACK_IMPORTED_MODULE_1__.animationPresets[animationPreset],\n        randomSeed: generateSeed(\"quarter-circle\", animationIndex)\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: animation.ref,\n        className: \"h-full\",\n        style: animation.style,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_RoundedShapes__WEBPACK_IMPORTED_MODULE_6__.QuarterCircle, {\n            ...props\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\AnimatedShapes.tsx\",\n            lineNumber: 260,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\AnimatedShapes.tsx\",\n        lineNumber: 259,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/shapes/AnimatedShapes.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/shapes/Circle.tsx":
/*!******************************************!*\
  !*** ./src/components/shapes/Circle.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SoftCircle: () => (/* binding */ SoftCircle),\n/* harmony export */   \"default\": () => (/* binding */ Circle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nconst sizeClasses = {\n    sm: \"w-8 h-8\",\n    md: \"w-16 h-16\",\n    lg: \"w-24 h-24\",\n    xl: \"w-32 h-32\"\n};\nconst colorClasses = {\n    red: \"bg-brand-red\",\n    yellow: \"bg-brand-yellow\",\n    blue: \"bg-brand-blue\",\n    black: \"bg-black\",\n    white: \"bg-white border border-black\"\n};\nfunction Circle({ size = \"md\", color = \"red\", className = \"\" }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `rounded-full ${sizeClasses[size]} ${colorClasses[color]} ${className}`\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\Circle.tsx\",\n        lineNumber: 28,\n        columnNumber: 5\n    }, this);\n}\n// Soft Circle with organic feel (inspired by logo roundedness)\nfunction SoftCircle({ size = \"md\", color = \"red\", className = \"\" }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `rounded-full ${sizeClasses[size]} ${colorClasses[color]} ${className}`,\n        style: {\n            filter: \"blur(0.5px)\"\n        }\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\Circle.tsx\",\n        lineNumber: 41,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/shapes/Circle.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/shapes/HalfCircle.tsx":
/*!**********************************************!*\
  !*** ./src/components/shapes/HalfCircle.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HalfCircle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nconst sizeClasses = {\n    sm: \"w-8 h-4\",\n    md: \"w-16 h-8\",\n    lg: \"w-24 h-12\",\n    xl: \"w-32 h-16\"\n};\nconst colorClasses = {\n    red: \"bg-bauhaus-red\",\n    yellow: \"bg-bauhaus-yellow\",\n    blue: \"bg-bauhaus-blue\",\n    black: \"bg-bauhaus-black\",\n    white: \"bg-bauhaus-white border border-bauhaus-black\"\n};\nconst directionClasses = {\n    top: \"rounded-t-full\",\n    bottom: \"rounded-b-full\",\n    left: \"rounded-l-full\",\n    right: \"rounded-r-full\"\n};\nconst getRotationClasses = (direction)=>{\n    switch(direction){\n        case \"left\":\n            return \"w-4 h-8 md:w-8 md:h-16 lg:w-12 lg:h-24 xl:w-16 xl:h-32\";\n        case \"right\":\n            return \"w-4 h-8 md:w-8 md:h-16 lg:w-12 lg:h-24 xl:w-16 xl:h-32\";\n        default:\n            return \"\";\n    }\n};\nfunction HalfCircle({ size = \"md\", color = \"red\", direction = \"top\", className = \"\" }) {\n    const rotationClasses = getRotationClasses(direction);\n    const finalSizeClasses = rotationClasses || sizeClasses[size];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `${finalSizeClasses} ${colorClasses[color]} ${directionClasses[direction]} ${className}`\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\HalfCircle.tsx\",\n        lineNumber: 51,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/shapes/HalfCircle.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/shapes/Rectangle.tsx":
/*!*********************************************!*\
  !*** ./src/components/shapes/Rectangle.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PillRectangle: () => (/* binding */ PillRectangle),\n/* harmony export */   RoundedRectangle: () => (/* binding */ RoundedRectangle),\n/* harmony export */   \"default\": () => (/* binding */ Rectangle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nconst widthClasses = {\n    sm: \"w-12\",\n    md: \"w-24\",\n    lg: \"w-32\",\n    xl: \"w-48\"\n};\nconst heightClasses = {\n    sm: \"h-8\",\n    md: \"h-16\",\n    lg: \"h-24\",\n    xl: \"h-32\"\n};\nconst colorClasses = {\n    red: \"bg-brand-red\",\n    yellow: \"bg-brand-yellow\",\n    blue: \"bg-brand-blue\",\n    black: \"bg-black\",\n    white: \"bg-white border border-black\"\n};\nfunction Rectangle({ width = \"md\", height = \"md\", color = \"blue\", className = \"\" }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `${widthClasses[width]} ${heightClasses[height]} ${colorClasses[color]} ${className}`\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\Rectangle.tsx\",\n        lineNumber: 37,\n        columnNumber: 5\n    }, this);\n}\n// Rounded Rectangle (inspired by logo's soft corners)\nfunction RoundedRectangle({ width = \"md\", height = \"md\", color = \"blue\", className = \"\" }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `rounded-3xl ${widthClasses[width]} ${heightClasses[height]} ${colorClasses[color]} ${className}`\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\Rectangle.tsx\",\n        lineNumber: 51,\n        columnNumber: 5\n    }, this);\n}\n// Pill shape (very rounded rectangle)\nfunction PillRectangle({ width = \"md\", height = \"md\", color = \"yellow\", className = \"\" }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `rounded-full ${widthClasses[width]} ${heightClasses[height]} ${colorClasses[color]} ${className}`\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\Rectangle.tsx\",\n        lineNumber: 65,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9zaGFwZXMvUmVjdGFuZ2xlLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFPQSxNQUFNQSxlQUFlO0lBQ25CQyxJQUFJO0lBQ0pDLElBQUk7SUFDSkMsSUFBSTtJQUNKQyxJQUFJO0FBQ047QUFFQSxNQUFNQyxnQkFBZ0I7SUFDcEJKLElBQUk7SUFDSkMsSUFBSTtJQUNKQyxJQUFJO0lBQ0pDLElBQUk7QUFDTjtBQUVBLE1BQU1FLGVBQWU7SUFDbkJDLEtBQUs7SUFDTEMsUUFBUTtJQUNSQyxNQUFNO0lBQ05DLE9BQU87SUFDUEMsT0FBTztBQUNUO0FBRWUsU0FBU0MsVUFBVSxFQUNoQ0MsUUFBUSxJQUFJLEVBQ1pDLFNBQVMsSUFBSSxFQUNiQyxRQUFRLE1BQU0sRUFDZEMsWUFBWSxFQUFFLEVBQ0M7SUFDZixxQkFDRSw4REFBQ0M7UUFDQ0QsV0FBVyxDQUFDLEVBQUVoQixZQUFZLENBQUNhLE1BQU0sQ0FBQyxDQUFDLEVBQUVSLGFBQWEsQ0FBQ1MsT0FBTyxDQUFDLENBQUMsRUFBRVIsWUFBWSxDQUFDUyxNQUFNLENBQUMsQ0FBQyxFQUFFQyxVQUFVLENBQUM7Ozs7OztBQUd0RztBQUVBLHNEQUFzRDtBQUMvQyxTQUFTRSxpQkFBaUIsRUFDL0JMLFFBQVEsSUFBSSxFQUNaQyxTQUFTLElBQUksRUFDYkMsUUFBUSxNQUFNLEVBQ2RDLFlBQVksRUFBRSxFQUNDO0lBQ2YscUJBQ0UsOERBQUNDO1FBQ0NELFdBQVcsQ0FBQyxZQUFZLEVBQUVoQixZQUFZLENBQUNhLE1BQU0sQ0FBQyxDQUFDLEVBQUVSLGFBQWEsQ0FBQ1MsT0FBTyxDQUFDLENBQUMsRUFBRVIsWUFBWSxDQUFDUyxNQUFNLENBQUMsQ0FBQyxFQUFFQyxVQUFVLENBQUM7Ozs7OztBQUdsSDtBQUVBLHNDQUFzQztBQUMvQixTQUFTRyxjQUFjLEVBQzVCTixRQUFRLElBQUksRUFDWkMsU0FBUyxJQUFJLEVBQ2JDLFFBQVEsUUFBUSxFQUNoQkMsWUFBWSxFQUFFLEVBQ0M7SUFDZixxQkFDRSw4REFBQ0M7UUFDQ0QsV0FBVyxDQUFDLGFBQWEsRUFBRWhCLFlBQVksQ0FBQ2EsTUFBTSxDQUFDLENBQUMsRUFBRVIsYUFBYSxDQUFDUyxPQUFPLENBQUMsQ0FBQyxFQUFFUixZQUFZLENBQUNTLE1BQU0sQ0FBQyxDQUFDLEVBQUVDLFVBQVUsQ0FBQzs7Ozs7O0FBR25IIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmF2aGF1cy13ZWJzaXRlLy4vc3JjL2NvbXBvbmVudHMvc2hhcGVzL1JlY3RhbmdsZS50c3g/OGUzMiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbnRlcmZhY2UgUmVjdGFuZ2xlUHJvcHMge1xuICB3aWR0aD86ICdzbScgfCAnbWQnIHwgJ2xnJyB8ICd4bCdcbiAgaGVpZ2h0PzogJ3NtJyB8ICdtZCcgfCAnbGcnIHwgJ3hsJ1xuICBjb2xvcj86ICdyZWQnIHwgJ3llbGxvdycgfCAnYmx1ZScgfCAnYmxhY2snIHwgJ3doaXRlJ1xuICBjbGFzc05hbWU/OiBzdHJpbmdcbn1cblxuY29uc3Qgd2lkdGhDbGFzc2VzID0ge1xuICBzbTogJ3ctMTInLFxuICBtZDogJ3ctMjQnLFxuICBsZzogJ3ctMzInLCBcbiAgeGw6ICd3LTQ4J1xufVxuXG5jb25zdCBoZWlnaHRDbGFzc2VzID0ge1xuICBzbTogJ2gtOCcsXG4gIG1kOiAnaC0xNicsXG4gIGxnOiAnaC0yNCcsXG4gIHhsOiAnaC0zMidcbn1cblxuY29uc3QgY29sb3JDbGFzc2VzID0ge1xuICByZWQ6ICdiZy1icmFuZC1yZWQnLFxuICB5ZWxsb3c6ICdiZy1icmFuZC15ZWxsb3cnLFxuICBibHVlOiAnYmctYnJhbmQtYmx1ZScsXG4gIGJsYWNrOiAnYmctYmxhY2snLFxuICB3aGl0ZTogJ2JnLXdoaXRlIGJvcmRlciBib3JkZXItYmxhY2snXG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJlY3RhbmdsZSh7XG4gIHdpZHRoID0gJ21kJyxcbiAgaGVpZ2h0ID0gJ21kJyxcbiAgY29sb3IgPSAnYmx1ZScsXG4gIGNsYXNzTmFtZSA9ICcnXG59OiBSZWN0YW5nbGVQcm9wcykge1xuICByZXR1cm4gKFxuICAgIDxkaXZcbiAgICAgIGNsYXNzTmFtZT17YCR7d2lkdGhDbGFzc2VzW3dpZHRoXX0gJHtoZWlnaHRDbGFzc2VzW2hlaWdodF19ICR7Y29sb3JDbGFzc2VzW2NvbG9yXX0gJHtjbGFzc05hbWV9YH1cbiAgICAvPlxuICApXG59XG5cbi8vIFJvdW5kZWQgUmVjdGFuZ2xlIChpbnNwaXJlZCBieSBsb2dvJ3Mgc29mdCBjb3JuZXJzKVxuZXhwb3J0IGZ1bmN0aW9uIFJvdW5kZWRSZWN0YW5nbGUoe1xuICB3aWR0aCA9ICdtZCcsXG4gIGhlaWdodCA9ICdtZCcsXG4gIGNvbG9yID0gJ2JsdWUnLFxuICBjbGFzc05hbWUgPSAnJ1xufTogUmVjdGFuZ2xlUHJvcHMpIHtcbiAgcmV0dXJuIChcbiAgICA8ZGl2XG4gICAgICBjbGFzc05hbWU9e2Byb3VuZGVkLTN4bCAke3dpZHRoQ2xhc3Nlc1t3aWR0aF19ICR7aGVpZ2h0Q2xhc3Nlc1toZWlnaHRdfSAke2NvbG9yQ2xhc3Nlc1tjb2xvcl19ICR7Y2xhc3NOYW1lfWB9XG4gICAgLz5cbiAgKVxufVxuXG4vLyBQaWxsIHNoYXBlICh2ZXJ5IHJvdW5kZWQgcmVjdGFuZ2xlKVxuZXhwb3J0IGZ1bmN0aW9uIFBpbGxSZWN0YW5nbGUoe1xuICB3aWR0aCA9ICdtZCcsXG4gIGhlaWdodCA9ICdtZCcsXG4gIGNvbG9yID0gJ3llbGxvdycsXG4gIGNsYXNzTmFtZSA9ICcnXG59OiBSZWN0YW5nbGVQcm9wcykge1xuICByZXR1cm4gKFxuICAgIDxkaXZcbiAgICAgIGNsYXNzTmFtZT17YHJvdW5kZWQtZnVsbCAke3dpZHRoQ2xhc3Nlc1t3aWR0aF19ICR7aGVpZ2h0Q2xhc3Nlc1toZWlnaHRdfSAke2NvbG9yQ2xhc3Nlc1tjb2xvcl19ICR7Y2xhc3NOYW1lfWB9XG4gICAgLz5cbiAgKVxufVxuIl0sIm5hbWVzIjpbIndpZHRoQ2xhc3NlcyIsInNtIiwibWQiLCJsZyIsInhsIiwiaGVpZ2h0Q2xhc3NlcyIsImNvbG9yQ2xhc3NlcyIsInJlZCIsInllbGxvdyIsImJsdWUiLCJibGFjayIsIndoaXRlIiwiUmVjdGFuZ2xlIiwid2lkdGgiLCJoZWlnaHQiLCJjb2xvciIsImNsYXNzTmFtZSIsImRpdiIsIlJvdW5kZWRSZWN0YW5nbGUiLCJQaWxsUmVjdGFuZ2xlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/shapes/Rectangle.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/shapes/RoundedShapes.tsx":
/*!*************************************************!*\
  !*** ./src/components/shapes/RoundedShapes.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Blob: () => (/* binding */ Blob),\n/* harmony export */   HalfCircle: () => (/* binding */ HalfCircle),\n/* harmony export */   Pill: () => (/* binding */ Pill),\n/* harmony export */   QuarterCircle: () => (/* binding */ QuarterCircle),\n/* harmony export */   RoundedRect: () => (/* binding */ RoundedRect),\n/* harmony export */   RoundedSquareWithCutout: () => (/* binding */ RoundedSquareWithCutout),\n/* harmony export */   SoftGrid: () => (/* binding */ SoftGrid)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\n// Rounded Rectangle with soft corners\nfunction RoundedRect({ className = \"\", color = \"blue\" }) {\n    const colorClasses = {\n        red: \"bg-brand-red\",\n        yellow: \"bg-brand-yellow\",\n        blue: \"bg-brand-blue\",\n        black: \"bg-black\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `${colorClasses[color]} rounded-3xl ${className}`\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\RoundedShapes.tsx\",\n        lineNumber: 16,\n        columnNumber: 5\n    }, this);\n}\n// Pill shape (very rounded rectangle)\nfunction Pill({ className = \"\", color = \"yellow\" }) {\n    const colorClasses = {\n        red: \"bg-brand-red\",\n        yellow: \"bg-brand-yellow\",\n        blue: \"bg-brand-blue\",\n        black: \"bg-black\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `${colorClasses[color]} rounded-full ${className}`\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\RoundedShapes.tsx\",\n        lineNumber: 30,\n        columnNumber: 5\n    }, this);\n}\n// Organic blob shape using CSS\nfunction Blob({ className = \"\", color = \"red\" }) {\n    const colorClasses = {\n        red: \"bg-brand-red\",\n        yellow: \"bg-brand-yellow\",\n        blue: \"bg-brand-blue\",\n        black: \"bg-black\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `${colorClasses[color]} ${className}`,\n        style: {\n            borderRadius: \"60% 40% 30% 70% / 60% 30% 70% 40%\"\n        }\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\RoundedShapes.tsx\",\n        lineNumber: 44,\n        columnNumber: 5\n    }, this);\n}\n// Half circle (like in your references)\nfunction HalfCircle({ className = \"\", color = \"blue\", direction = \"right\" }) {\n    const colorClasses = {\n        red: \"bg-brand-red\",\n        yellow: \"bg-brand-yellow\",\n        blue: \"bg-brand-blue\",\n        black: \"bg-black\"\n    };\n    const directionClasses = {\n        right: \"rounded-l-full\",\n        left: \"rounded-r-full\",\n        top: \"rounded-b-full\",\n        bottom: \"rounded-t-full\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `${colorClasses[color]} ${directionClasses[direction]} ${className}`\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\RoundedShapes.tsx\",\n        lineNumber: 70,\n        columnNumber: 5\n    }, this);\n}\n// Quarter circle\nfunction QuarterCircle({ className = \"\", color = \"yellow\", corner = \"top-left\" }) {\n    const colorClasses = {\n        red: \"bg-brand-red\",\n        yellow: \"bg-brand-yellow\",\n        blue: \"bg-brand-blue\",\n        black: \"bg-black\"\n    };\n    const cornerClasses = {\n        \"top-left\": \"rounded-br-full\",\n        \"top-right\": \"rounded-bl-full\",\n        \"bottom-left\": \"rounded-tr-full\",\n        \"bottom-right\": \"rounded-tl-full\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `${colorClasses[color]} ${cornerClasses[corner]} ${className}`\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\RoundedShapes.tsx\",\n        lineNumber: 91,\n        columnNumber: 5\n    }, this);\n}\n// Rounded square with cutout (inspired by your logo)\nfunction RoundedSquareWithCutout({ className = \"\", color = \"black\" }) {\n    const colorClasses = {\n        red: \"bg-brand-red\",\n        yellow: \"bg-brand-yellow\",\n        blue: \"bg-brand-blue\",\n        black: \"bg-black\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `relative ${className}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `${colorClasses[color]} rounded-3xl w-full h-full`\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\RoundedShapes.tsx\",\n                lineNumber: 106,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-brand-background rounded-full w-1/2 h-1/2\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\RoundedShapes.tsx\",\n                lineNumber: 107,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\RoundedShapes.tsx\",\n        lineNumber: 105,\n        columnNumber: 5\n    }, this);\n}\n// Soft grid overlay (like in the DSM reference)\nfunction SoftGrid({ className = \"\", opacity = \"default\" }) {\n    const opacityClass = opacity === \"hero\" ? \"opacity-40\" : \"opacity-20\";\n    const strokeOpacity = opacity === \"hero\" ? \"0.5\" : \"0.3\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `absolute inset-0 ${opacityClass} ${className}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            width: \"100%\",\n            height: \"100%\",\n            className: \"w-full h-full\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"defs\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pattern\", {\n                        id: \"grid\",\n                        width: \"40\",\n                        height: \"40\",\n                        patternUnits: \"userSpaceOnUse\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            d: \"M 40 0 L 0 0 0 40\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            strokeWidth: \"1\",\n                            opacity: strokeOpacity\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\RoundedShapes.tsx\",\n                            lineNumber: 122,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\RoundedShapes.tsx\",\n                        lineNumber: 121,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\RoundedShapes.tsx\",\n                    lineNumber: 120,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                    width: \"100%\",\n                    height: \"100%\",\n                    fill: \"url(#grid)\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\RoundedShapes.tsx\",\n                    lineNumber: 125,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\RoundedShapes.tsx\",\n            lineNumber: 119,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\RoundedShapes.tsx\",\n        lineNumber: 118,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/shapes/RoundedShapes.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/shapes/Triangle.tsx":
/*!********************************************!*\
  !*** ./src/components/shapes/Triangle.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Triangle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nconst sizeClasses = {\n    sm: \"w-0 h-0 border-l-[16px] border-r-[16px] border-b-[28px]\",\n    md: \"w-0 h-0 border-l-[24px] border-r-[24px] border-b-[42px]\",\n    lg: \"w-0 h-0 border-l-[32px] border-r-[32px] border-b-[56px]\",\n    xl: \"w-0 h-0 border-l-[48px] border-r-[48px] border-b-[84px]\"\n};\nconst getTriangleClasses = (size, color, direction)=>{\n    const colorMap = {\n        red: \"#e94436\",\n        yellow: \"#ffc527\",\n        blue: \"#434897\",\n        black: \"#000000\",\n        white: \"#ffffff\"\n    };\n    const baseSize = sizeClasses[size];\n    const triangleColor = colorMap[color];\n    switch(direction){\n        case \"up\":\n            return `${baseSize} border-l-transparent border-r-transparent`;\n        case \"down\":\n            return baseSize.replace(\"border-b-\", \"border-t-\") + \" border-l-transparent border-r-transparent\";\n        case \"left\":\n            return baseSize.replace(\"border-l-\", \"border-r-\").replace(\"border-r-\", \"border-t-\").replace(\"border-b-\", \"border-l-\") + \" border-t-transparent border-b-transparent\";\n        case \"right\":\n            return baseSize.replace(\"border-r-\", \"border-l-\").replace(\"border-l-\", \"border-t-\").replace(\"border-b-\", \"border-r-\") + \" border-t-transparent border-b-transparent\";\n        default:\n            return `${baseSize} border-l-transparent border-r-transparent`;\n    }\n};\nfunction Triangle({ size = \"md\", color = \"yellow\", direction = \"up\", className = \"\" }) {\n    const triangleClasses = getTriangleClasses(size, color, direction);\n    const style = {\n        borderBottomColor: direction === \"up\" ? color === \"red\" ? \"#e94436\" : color === \"yellow\" ? \"#ffc527\" : color === \"blue\" ? \"#434897\" : color === \"black\" ? \"#000000\" : \"#ffffff\" : \"transparent\",\n        borderTopColor: direction === \"down\" ? color === \"red\" ? \"#e94436\" : color === \"yellow\" ? \"#ffc527\" : color === \"blue\" ? \"#434897\" : color === \"black\" ? \"#000000\" : \"#ffffff\" : \"transparent\",\n        borderLeftColor: direction === \"right\" ? color === \"red\" ? \"#e94436\" : color === \"yellow\" ? \"#ffc527\" : color === \"blue\" ? \"#434897\" : color === \"black\" ? \"#000000\" : \"#ffffff\" : \"transparent\",\n        borderRightColor: direction === \"left\" ? color === \"red\" ? \"#e94436\" : color === \"yellow\" ? \"#ffc527\" : color === \"blue\" ? \"#434897\" : color === \"black\" ? \"#000000\" : \"#ffffff\" : \"transparent\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `${triangleClasses} ${className}`,\n        style: style\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\Triangle.tsx\",\n        lineNumber: 57,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/shapes/Triangle.tsx\n");

/***/ }),

/***/ "(ssr)/./src/hooks/useScrollAnimation.ts":
/*!*****************************************!*\
  !*** ./src/hooks/useScrollAnimation.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   animationPresets: () => (/* binding */ animationPresets),\n/* harmony export */   useScrollAnimation: () => (/* binding */ useScrollAnimation)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ useScrollAnimation,animationPresets auto */ \nfunction useScrollAnimation(config) {\n    const [scrollY, setScrollY] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(0);\n    const elementRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const [randomFactors, setRandomFactors] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        x: 0,\n        y: 0\n    });\n    // Generate consistent random factors based on seed with more varied directions\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        const seed = config.randomSeed || Math.random();\n        // Use multiple different seeds to create more varied movement patterns\n        const xSeed = seed * 12.9898;\n        const ySeed = seed * 78.233;\n        const angleSeed = seed * 45.164;\n        // Generate base random values\n        const baseX = Math.sin(xSeed) * 43758.5453 % 1;\n        const baseY = Math.sin(ySeed) * 43758.5453 % 1;\n        const angle = Math.sin(angleSeed) * 43758.5453 % 1;\n        // Create more dynamic directional patterns\n        // Use angle to create diagonal movements and varied directions\n        const angleRad = angle * Math.PI * 2 // Full circle\n        ;\n        const magnitude = 0.8 + Math.abs(baseX) * 0.4 // Vary magnitude between 0.8-1.2\n        ;\n        setRandomFactors({\n            x: Math.cos(angleRad) * magnitude,\n            y: Math.sin(angleRad) * magnitude\n        });\n    }, [\n        config.randomSeed\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        const handleScroll = ()=>{\n            setScrollY(window.scrollY);\n        };\n        // Use passive listener for better performance\n        window.addEventListener(\"scroll\", handleScroll, {\n            passive: true\n        });\n        return ()=>{\n            window.removeEventListener(\"scroll\", handleScroll);\n        };\n    }, []);\n    // Calculate transform values\n    const getTransform = ()=>{\n        if (!elementRef.current) return \"translate3d(0, 0, 0)\";\n        const rect = elementRef.current.getBoundingClientRect();\n        const elementTop = rect.top + scrollY;\n        const scrollProgress = (scrollY - elementTop + window.innerHeight) / (window.innerHeight + rect.height);\n        // Smooth easing function\n        const easeInOutQuad = (t)=>{\n            return t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t;\n        };\n        const easedProgress = easeInOutQuad(Math.max(0, Math.min(1, scrollProgress)));\n        let translateX = 0;\n        let translateY = 0;\n        if (config.direction === \"x\" || config.direction === \"both\") {\n            translateX = easedProgress * config.intensity * randomFactors.x * config.speed;\n        }\n        if (config.direction === \"y\" || config.direction === \"both\") {\n            translateY = easedProgress * config.intensity * randomFactors.y * config.speed;\n        }\n        return `translate3d(${translateX}px, ${translateY}px, 0)`;\n    };\n    return {\n        ref: elementRef,\n        style: {\n            transform: getTransform(),\n            transition: \"transform 0.1s ease-out\",\n            willChange: \"transform\"\n        }\n    };\n}\n// Predefined animation presets for different shape types with varied movement\nconst animationPresets = {\n    subtle: {\n        direction: \"both\",\n        intensity: 25,\n        speed: 0.6\n    },\n    gentle: {\n        direction: \"both\",\n        intensity: 35,\n        speed: 0.8\n    },\n    dynamic: {\n        direction: \"both\",\n        intensity: 55,\n        speed: 1.2\n    },\n    flowing: {\n        direction: \"both\",\n        intensity: 40,\n        speed: 0.9\n    },\n    energetic: {\n        direction: \"both\",\n        intensity: 60,\n        speed: 1.4\n    },\n    drift: {\n        direction: \"both\",\n        intensity: 30,\n        speed: 0.5\n    },\n    pulse: {\n        direction: \"both\",\n        intensity: 45,\n        speed: 1.1\n    },\n    float: {\n        direction: \"both\",\n        intensity: 20,\n        speed: 0.4\n    },\n    horizontal: {\n        direction: \"x\",\n        intensity: 40,\n        speed: 0.8\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/useScrollAnimation.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"513ac26c769d\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmF2aGF1cy13ZWJzaXRlLy4vc3JjL2FwcC9nbG9iYWxzLmNzcz9lOTdlIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiNTEzYWMyNmM3NjlkXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\nconst metadata = {\n    title: \"Navhaus - What matters, made real\",\n    description: \"Navhaus is a design and development studio that builds bold, efficient, and meaningful digital experiences — nothing more, nothing less.\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 16,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 15,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQ3NCO0FBRWYsTUFBTUEsV0FBcUI7SUFDaENDLE9BQU87SUFDUEMsYUFBYTtBQUNmLEVBQUM7QUFFYyxTQUFTQyxXQUFXLEVBQ2pDQyxRQUFRLEVBR1Q7SUFDQyxxQkFDRSw4REFBQ0M7UUFBS0MsTUFBSztrQkFDVCw0RUFBQ0M7c0JBQU1IOzs7Ozs7Ozs7OztBQUdiIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmF2aGF1cy13ZWJzaXRlLy4vc3JjL2FwcC9sYXlvdXQudHN4PzU3YTkiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHR5cGUgeyBNZXRhZGF0YSB9IGZyb20gJ25leHQnXG5pbXBvcnQgJy4vZ2xvYmFscy5jc3MnXG5cbmV4cG9ydCBjb25zdCBtZXRhZGF0YTogTWV0YWRhdGEgPSB7XG4gIHRpdGxlOiAnTmF2aGF1cyAtIFdoYXQgbWF0dGVycywgbWFkZSByZWFsJyxcbiAgZGVzY3JpcHRpb246ICdOYXZoYXVzIGlzIGEgZGVzaWduIGFuZCBkZXZlbG9wbWVudCBzdHVkaW8gdGhhdCBidWlsZHMgYm9sZCwgZWZmaWNpZW50LCBhbmQgbWVhbmluZ2Z1bCBkaWdpdGFsIGV4cGVyaWVuY2VzIOKAlCBub3RoaW5nIG1vcmUsIG5vdGhpbmcgbGVzcy4nLFxufVxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBSb290TGF5b3V0KHtcbiAgY2hpbGRyZW4sXG59OiB7XG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGVcbn0pIHtcbiAgcmV0dXJuIChcbiAgICA8aHRtbCBsYW5nPVwiZW5cIj5cbiAgICAgIDxib2R5PntjaGlsZHJlbn08L2JvZHk+XG4gICAgPC9odG1sPlxuICApXG59XG4iXSwibmFtZXMiOlsibWV0YWRhdGEiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiUm9vdExheW91dCIsImNoaWxkcmVuIiwiaHRtbCIsImxhbmciLCJib2R5Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_layout_PageWrapper__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/layout/PageWrapper */ \"(rsc)/./src/components/layout/PageWrapper.tsx\");\n/* harmony import */ var _components_compositions_OrganicComposition__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/compositions/OrganicComposition */ \"(rsc)/./src/components/compositions/OrganicComposition.tsx\");\n/* harmony import */ var _components_shapes_AnimatedShapes__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/shapes/AnimatedShapes */ \"(rsc)/./src/components/shapes/AnimatedShapes.tsx\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_4__);\n\n\n\n\n\nfunction Home() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_PageWrapper__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"relative px-6 md:px-12 lg:px-24 pt-16 pb-16 overflow-hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-16 items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-hero font-bold leading-tight\",\n                                        children: \"What matters, made real.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 20,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xl md:text-2xl leading-relaxed text-gray-700 max-w-lg\",\n                                        children: \"Navhaus is a design and development studio that builds bold, efficient, and meaningful digital experiences — nothing more, nothing less.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 23,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                        href: \"/contact\",\n                                        className: \"btn-red inline-block\",\n                                        children: \"Start Your Project\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 27,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 19,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative h-96 lg:h-[500px]\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_compositions_OrganicComposition__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    variant: \"hero\",\n                                    className: \"w-full h-full\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 34,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 33,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 17,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 16,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 15,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"px-6 md:px-12 lg:px-24 py-8 bg-brand-background\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-display font-bold text-center mb-16\",\n                            children: \"How We Work\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 43,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-24\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative h-64 w-full\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shapes_AnimatedShapes__WEBPACK_IMPORTED_MODULE_3__.AnimatedSoftGrid, {\n                                                        className: \"w-full h-full text-black\",\n                                                        opacity: \"hero\",\n                                                        animationPreset: \"subtle\",\n                                                        animationIndex: 0\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 51,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 50,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative flex justify-center lg:justify-start items-center h-full\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shapes_AnimatedShapes__WEBPACK_IMPORTED_MODULE_3__.AnimatedSoftCircle, {\n                                                        size: \"xl\",\n                                                        color: \"blue\",\n                                                        className: \"w-32 h-32\",\n                                                        animationPreset: \"gentle\",\n                                                        animationIndex: 1\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 55,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 54,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute top-4 right-8 opacity-80\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shapes_AnimatedShapes__WEBPACK_IMPORTED_MODULE_3__.AnimatedRoundedRectangle, {\n                                                        width: \"md\",\n                                                        height: \"sm\",\n                                                        color: \"yellow\",\n                                                        className: \"w-12 h-8\",\n                                                        animationPreset: \"flowing\",\n                                                        animationIndex: 2\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 59,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 58,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute bottom-8 left-16 opacity-80\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shapes_AnimatedShapes__WEBPACK_IMPORTED_MODULE_3__.AnimatedSoftCircle, {\n                                                        size: \"md\",\n                                                        color: \"red\",\n                                                        className: \"w-10 h-10\",\n                                                        animationPreset: \"gentle\",\n                                                        animationIndex: 3\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 62,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 61,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute top-1/2 right-4 opacity-70\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shapes_AnimatedShapes__WEBPACK_IMPORTED_MODULE_3__.AnimatedTriangle, {\n                                                        size: \"md\",\n                                                        color: \"red\",\n                                                        direction: \"up\",\n                                                        className: \"w-8 h-8\",\n                                                        animationPreset: \"energetic\",\n                                                        animationIndex: 4\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 65,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 64,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute top-8 left-8 opacity-60\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shapes_AnimatedShapes__WEBPACK_IMPORTED_MODULE_3__.AnimatedRoundedRectangle, {\n                                                        width: \"sm\",\n                                                        height: \"sm\",\n                                                        color: \"red\",\n                                                        className: \"w-6 h-3\",\n                                                        animationPreset: \"drift\",\n                                                        animationIndex: 5\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 68,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 67,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute bottom-4 right-16 opacity-70\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shapes_AnimatedShapes__WEBPACK_IMPORTED_MODULE_3__.AnimatedSoftCircle, {\n                                                        size: \"sm\",\n                                                        color: \"yellow\",\n                                                        className: \"w-6 h-6\",\n                                                        animationPreset: \"float\",\n                                                        animationIndex: 6\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 71,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 70,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute top-16 right-20 opacity-50\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shapes_AnimatedShapes__WEBPACK_IMPORTED_MODULE_3__.AnimatedTriangle, {\n                                                        size: \"sm\",\n                                                        color: \"blue\",\n                                                        direction: \"up\",\n                                                        className: \"w-4 h-4\",\n                                                        animationPreset: \"pulse\",\n                                                        animationIndex: 7\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 74,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 73,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute bottom-16 left-4 opacity-60\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shapes_AnimatedShapes__WEBPACK_IMPORTED_MODULE_3__.AnimatedRoundedRectangle, {\n                                                        width: \"sm\",\n                                                        height: \"sm\",\n                                                        color: \"blue\",\n                                                        className: \"w-4 h-4\",\n                                                        animationPreset: \"flowing\",\n                                                        animationIndex: 8\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 77,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 76,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute top-1/3 left-1/3 opacity-50\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shapes_AnimatedShapes__WEBPACK_IMPORTED_MODULE_3__.AnimatedSoftCircle, {\n                                                        size: \"sm\",\n                                                        color: \"yellow\",\n                                                        className: \"w-4 h-4\",\n                                                        animationPreset: \"drift\",\n                                                        animationIndex: 9\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 80,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 79,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 48,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-6 text-center lg:text-left\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-heading font-bold\",\n                                                    children: \"Listen\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 84,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-body text-gray-700 leading-relaxed text-lg\",\n                                                    children: \"We start by understanding exactly what you're trying to do — and more importantly, why.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 85,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 83,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 47,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-6 text-center lg:text-right lg:order-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-heading font-bold\",\n                                                    children: \"Design\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 94,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-body text-gray-700 leading-relaxed text-lg\",\n                                                    children: \"We strip away noise, leaving only what needs to be there. Everything has a reason.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 95,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 93,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative h-64 w-full lg:order-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shapes_AnimatedShapes__WEBPACK_IMPORTED_MODULE_3__.AnimatedSoftGrid, {\n                                                        className: \"w-full h-full text-black\",\n                                                        opacity: \"hero\",\n                                                        animationPreset: \"subtle\",\n                                                        animationIndex: 10\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 102,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 101,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative flex justify-center lg:justify-end items-center h-full\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shapes_AnimatedShapes__WEBPACK_IMPORTED_MODULE_3__.AnimatedRoundedRectangle, {\n                                                        width: \"xl\",\n                                                        height: \"xl\",\n                                                        color: \"yellow\",\n                                                        className: \"w-32 h-24\",\n                                                        animationPreset: \"gentle\",\n                                                        animationIndex: 11\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 106,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 105,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute top-6 left-8 opacity-80\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shapes_AnimatedShapes__WEBPACK_IMPORTED_MODULE_3__.AnimatedSoftCircle, {\n                                                        size: \"lg\",\n                                                        color: \"blue\",\n                                                        className: \"w-14 h-14\",\n                                                        animationPreset: \"energetic\",\n                                                        animationIndex: 12\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 110,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 109,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute bottom-4 right-20 opacity-80\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shapes_AnimatedShapes__WEBPACK_IMPORTED_MODULE_3__.AnimatedTriangle, {\n                                                        size: \"md\",\n                                                        color: \"red\",\n                                                        direction: \"up\",\n                                                        className: \"w-8 h-8\",\n                                                        animationPreset: \"dynamic\",\n                                                        animationIndex: 13\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 113,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 112,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute top-1/3 left-4 opacity-70\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shapes_AnimatedShapes__WEBPACK_IMPORTED_MODULE_3__.AnimatedRoundedRectangle, {\n                                                        width: \"sm\",\n                                                        height: \"sm\",\n                                                        color: \"blue\",\n                                                        className: \"w-6 h-6\",\n                                                        animationPreset: \"pulse\",\n                                                        animationIndex: 14\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 116,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 115,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute bottom-1/3 left-1/3 opacity-70\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shapes_AnimatedShapes__WEBPACK_IMPORTED_MODULE_3__.AnimatedSoftCircle, {\n                                                        size: \"sm\",\n                                                        color: \"red\",\n                                                        className: \"w-6 h-6\",\n                                                        animationPreset: \"flowing\",\n                                                        animationIndex: 15\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 119,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 118,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute top-4 right-4 opacity-60\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shapes_AnimatedShapes__WEBPACK_IMPORTED_MODULE_3__.AnimatedTriangle, {\n                                                        size: \"sm\",\n                                                        color: \"blue\",\n                                                        direction: \"up\",\n                                                        className: \"w-5 h-5\",\n                                                        animationPreset: \"drift\",\n                                                        animationIndex: 16\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 122,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 121,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute bottom-8 left-20 opacity-60\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shapes_AnimatedShapes__WEBPACK_IMPORTED_MODULE_3__.AnimatedRoundedRectangle, {\n                                                        width: \"sm\",\n                                                        height: \"md\",\n                                                        color: \"red\",\n                                                        className: \"w-3 h-8\",\n                                                        animationPreset: \"gentle\",\n                                                        animationIndex: 17\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 125,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 124,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute top-20 left-16 opacity-50\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shapes_AnimatedShapes__WEBPACK_IMPORTED_MODULE_3__.AnimatedSoftCircle, {\n                                                        size: \"sm\",\n                                                        color: \"yellow\",\n                                                        className: \"w-4 h-4\",\n                                                        animationPreset: \"float\",\n                                                        animationIndex: 18\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 128,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 127,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute bottom-20 right-8 opacity-60\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shapes_AnimatedShapes__WEBPACK_IMPORTED_MODULE_3__.AnimatedTriangle, {\n                                                        size: \"sm\",\n                                                        color: \"yellow\",\n                                                        direction: \"up\",\n                                                        className: \"w-3 h-3\",\n                                                        animationPreset: \"pulse\",\n                                                        animationIndex: 19\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 131,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 130,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute top-1/2 left-1/2 opacity-50\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shapes_AnimatedShapes__WEBPACK_IMPORTED_MODULE_3__.AnimatedRoundedRectangle, {\n                                                        width: \"sm\",\n                                                        height: \"sm\",\n                                                        color: \"blue\",\n                                                        className: \"w-3 h-3\",\n                                                        animationPreset: \"drift\",\n                                                        animationIndex: 20\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 134,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 133,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 99,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 92,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative h-64 w-full\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shapes_AnimatedShapes__WEBPACK_IMPORTED_MODULE_3__.AnimatedSoftGrid, {\n                                                        className: \"w-full h-full text-black\",\n                                                        opacity: \"hero\",\n                                                        animationPreset: \"subtle\",\n                                                        animationIndex: 21\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 144,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 143,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative flex justify-center lg:justify-start items-center h-full\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute bottom-12\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shapes_AnimatedShapes__WEBPACK_IMPORTED_MODULE_3__.AnimatedRoundedRectangle, {\n                                                                width: \"xl\",\n                                                                height: \"lg\",\n                                                                color: \"red\",\n                                                                className: \"w-40 h-12\",\n                                                                animationPreset: \"flowing\",\n                                                                animationIndex: 22\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 150,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 149,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute bottom-20 left-4\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shapes_AnimatedShapes__WEBPACK_IMPORTED_MODULE_3__.AnimatedRoundedRectangle, {\n                                                                width: \"xl\",\n                                                                height: \"lg\",\n                                                                color: \"blue\",\n                                                                className: \"w-32 h-12\",\n                                                                animationPreset: \"energetic\",\n                                                                animationIndex: 23\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 154,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 153,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute bottom-28 left-8\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shapes_AnimatedShapes__WEBPACK_IMPORTED_MODULE_3__.AnimatedRoundedRectangle, {\n                                                                width: \"lg\",\n                                                                height: \"lg\",\n                                                                color: \"yellow\",\n                                                                className: \"w-24 h-12\",\n                                                                animationPreset: \"pulse\",\n                                                                animationIndex: 24\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 158,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 157,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute bottom-16 right-4 opacity-80\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shapes_AnimatedShapes__WEBPACK_IMPORTED_MODULE_3__.AnimatedSoftCircle, {\n                                                                size: \"md\",\n                                                                color: \"yellow\",\n                                                                className: \"w-10 h-10\",\n                                                                animationPreset: \"dynamic\",\n                                                                animationIndex: 25\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 162,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 161,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute bottom-24 right-8 opacity-80\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shapes_AnimatedShapes__WEBPACK_IMPORTED_MODULE_3__.AnimatedSoftCircle, {\n                                                                size: \"sm\",\n                                                                color: \"red\",\n                                                                className: \"w-6 h-6\",\n                                                                animationPreset: \"drift\",\n                                                                animationIndex: 26\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 165,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 164,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute bottom-32 right-12 opacity-70\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shapes_AnimatedShapes__WEBPACK_IMPORTED_MODULE_3__.AnimatedTriangle, {\n                                                                size: \"sm\",\n                                                                color: \"blue\",\n                                                                direction: \"up\",\n                                                                className: \"w-6 h-6\",\n                                                                animationPreset: \"energetic\",\n                                                                animationIndex: 27\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 168,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 167,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute top-8 right-4 opacity-70\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shapes_AnimatedShapes__WEBPACK_IMPORTED_MODULE_3__.AnimatedTriangle, {\n                                                                size: \"md\",\n                                                                color: \"blue\",\n                                                                direction: \"up\",\n                                                                className: \"w-8 h-8\",\n                                                                animationPreset: \"flowing\",\n                                                                animationIndex: 28\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 172,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 171,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute top-16 left-4 opacity-60\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shapes_AnimatedShapes__WEBPACK_IMPORTED_MODULE_3__.AnimatedRoundedRectangle, {\n                                                                width: \"sm\",\n                                                                height: \"sm\",\n                                                                color: \"yellow\",\n                                                                className: \"w-6 h-6\",\n                                                                animationPreset: \"gentle\",\n                                                                animationIndex: 29\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 175,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 174,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute top-4 left-12 opacity-60\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shapes_AnimatedShapes__WEBPACK_IMPORTED_MODULE_3__.AnimatedSoftCircle, {\n                                                                size: \"sm\",\n                                                                color: \"red\",\n                                                                className: \"w-6 h-6\",\n                                                                animationPreset: \"float\",\n                                                                animationIndex: 30\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 178,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 177,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute top-12 right-16 opacity-50\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shapes_AnimatedShapes__WEBPACK_IMPORTED_MODULE_3__.AnimatedRoundedRectangle, {\n                                                                width: \"sm\",\n                                                                height: \"md\",\n                                                                color: \"red\",\n                                                                className: \"w-3 h-6\",\n                                                                animationPreset: \"pulse\",\n                                                                animationIndex: 31\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 181,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 180,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute top-20 left-16 opacity-50\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shapes_AnimatedShapes__WEBPACK_IMPORTED_MODULE_3__.AnimatedTriangle, {\n                                                                size: \"sm\",\n                                                                color: \"yellow\",\n                                                                direction: \"up\",\n                                                                className: \"w-4 h-4\",\n                                                                animationPreset: \"drift\",\n                                                                animationIndex: 32\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 184,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 183,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute top-6 left-20 opacity-40\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shapes_AnimatedShapes__WEBPACK_IMPORTED_MODULE_3__.AnimatedSoftCircle, {\n                                                                size: \"sm\",\n                                                                color: \"blue\",\n                                                                className: \"w-4 h-4\",\n                                                                animationPreset: \"float\",\n                                                                animationIndex: 33\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 187,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 186,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute bottom-8 left-20 opacity-60\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shapes_AnimatedShapes__WEBPACK_IMPORTED_MODULE_3__.AnimatedRoundedRectangle, {\n                                                                width: \"sm\",\n                                                                height: \"sm\",\n                                                                color: \"yellow\",\n                                                                className: \"w-4 h-4\",\n                                                                animationPreset: \"flowing\",\n                                                                animationIndex: 34\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 190,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 189,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 147,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 141,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-6 text-center lg:text-left\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-heading font-bold\",\n                                                    children: \"Build\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 195,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-body text-gray-700 leading-relaxed text-lg\",\n                                                    children: \"We ship clean code and scalable systems. Fast. Reliable. Yours to own.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 196,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 194,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 140,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 45,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 42,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 41,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"relative px-6 md:px-12 lg:px-24 py-16 md:py-24 overflow-hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto relative z-10\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-display font-bold text-center mb-8\",\n                            children: \"Things We Love Building\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 208,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-center text-gray-700 mb-16 max-w-3xl mx-auto\",\n                            children: \"No big case studies (yet), but here's what gets us fired up.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 209,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute top-0 left-0 opacity-60\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shapes_AnimatedShapes__WEBPACK_IMPORTED_MODULE_3__.AnimatedSoftGrid, {\n                                        className: \"w-24 h-24 text-black\",\n                                        animationPreset: \"float\",\n                                        animationIndex: 35\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 216,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shapes_AnimatedShapes__WEBPACK_IMPORTED_MODULE_3__.AnimatedSoftCircle, {\n                                        size: \"lg\",\n                                        color: \"red\",\n                                        className: \"absolute top-2 left-2 w-16 h-16\",\n                                        animationPreset: \"energetic\",\n                                        animationIndex: 36\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 217,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 215,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 214,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute top-1/2 right-0 opacity-60\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shapes_AnimatedShapes__WEBPACK_IMPORTED_MODULE_3__.AnimatedSoftGrid, {\n                                        className: \"w-32 h-32 text-black\",\n                                        animationPreset: \"drift\",\n                                        animationIndex: 37\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 222,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shapes_AnimatedShapes__WEBPACK_IMPORTED_MODULE_3__.AnimatedRoundedRectangle, {\n                                        width: \"lg\",\n                                        height: \"md\",\n                                        color: \"blue\",\n                                        className: \"absolute top-4 left-4 w-20 h-12\",\n                                        animationPreset: \"flowing\",\n                                        animationIndex: 38\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 223,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 221,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 220,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute bottom-0 left-1/3 opacity-60\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shapes_AnimatedShapes__WEBPACK_IMPORTED_MODULE_3__.AnimatedSoftGrid, {\n                                        className: \"w-28 h-28 text-black\",\n                                        animationPreset: \"pulse\",\n                                        animationIndex: 39\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 228,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shapes_AnimatedShapes__WEBPACK_IMPORTED_MODULE_3__.AnimatedTriangle, {\n                                        size: \"lg\",\n                                        color: \"yellow\",\n                                        direction: \"up\",\n                                        className: \"absolute top-3 left-3 w-18 h-18\",\n                                        animationPreset: \"dynamic\",\n                                        animationIndex: 40\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 229,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 227,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 226,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-8 lg:gap-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-brand-background border-2 border-bauhaus-black rounded-2xl p-8 space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-heading font-bold\",\n                                            children: \"High-performance marketing sites\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 235,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-body text-gray-700 leading-relaxed\",\n                                            children: \"Built to load fast, convert hard, and stay lean.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 236,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 234,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-brand-background border-2 border-bauhaus-black rounded-2xl p-8 space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-heading font-bold\",\n                                            children: \"Design systems that scale\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 243,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-body text-gray-700 leading-relaxed\",\n                                            children: \"Interfaces that grow without turning to chaos.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 244,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 242,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-brand-background border-2 border-bauhaus-black rounded-2xl p-8 space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-heading font-bold\",\n                                            children: \"Custom Gutenberg blocks\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 251,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-body text-gray-700 leading-relaxed\",\n                                            children: \"Purpose-built. No extra plugins. Just exactly what you need.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 252,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 250,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-brand-background border-2 border-bauhaus-black rounded-2xl p-8 space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-heading font-bold\",\n                                            children: \"API-connected web apps\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 259,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-body text-gray-700 leading-relaxed\",\n                                            children: \"Lightweight, maintainable, and fast as hell.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 260,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 258,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 232,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 207,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 206,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"px-6 md:px-12 lg:px-24 py-16 md:py-24 bg-brand-background\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-5xl mx-auto text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-display font-bold mb-8\",\n                            children: \"Who We're For\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 271,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-gray-700 leading-relaxed mb-12\",\n                            children: [\n                                \"We work with founders, marketers, and teams who value clarity over chaos.\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 273,\n                                    columnNumber: 86\n                                }, this),\n                                \"People who want:\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 272,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-8 text-left max-w-3xl mx-auto mb-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-body text-gray-700\",\n                                            children: \"• Smaller teams, fewer meetings\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 278,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-body text-gray-700\",\n                                            children: \"• Transparent communication\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 279,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 277,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-body text-gray-700\",\n                                            children: \"• Clean design, clean code\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 282,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-body text-gray-700\",\n                                            children: \"• Something real, built fast\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 283,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 281,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 276,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl font-bold text-gray-900\",\n                            children: \"Still new here? That just means we're hungrier to prove it.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 286,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 270,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 269,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"px-6 md:px-12 lg:px-24 py-16 md:py-24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-display font-bold text-center mb-8\",\n                            children: \"What We Build With\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 295,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-center text-gray-700 mb-16 max-w-4xl mx-auto\",\n                            children: \"We use tools that are fast, modern, and built to scale. Everything is chosen for clarity, performance, and long-term maintainability — no fluff, no filler.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 296,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 lg:grid-cols-3 gap-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-heading font-bold\",\n                                            children: \"Core Stack\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 303,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-bold text-lg mb-2\",\n                                                            children: \"WordPress (Custom Themes)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 306,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-700\",\n                                                            children: \"Built from scratch — clean, minimal, and made to last.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 307,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 305,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-bold text-lg mb-2\",\n                                                            children: \"Sage (Roots stack)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 310,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-700\",\n                                                            children: \"A modern workflow for WordPress, using Blade, Composer, and clean structure.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 311,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 309,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-bold text-lg mb-2\",\n                                                            children: \"Blade\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 314,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-700\",\n                                                            children: \"Elegant templating with clear separation between logic and layout.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 315,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 313,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-bold text-lg mb-2\",\n                                                            children: \"Gutenberg (Custom Blocks)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 318,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-700\",\n                                                            children: \"Native block development with PHP and JavaScript — no ACF Pro.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 319,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 317,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-bold text-lg mb-2\",\n                                                            children: \"PHP\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 322,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-700\",\n                                                            children: \"Lightweight and structured backend logic.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 323,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 321,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-bold text-lg mb-2\",\n                                                            children: \"JavaScript (Vanilla)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 326,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-700\",\n                                                            children: \"Used for interactions and custom block logic — nothing unnecessary.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 327,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 325,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-bold text-lg mb-2\",\n                                                            children: \"Tailwind CSS\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 330,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-700\",\n                                                            children: \"Utility-first styling that keeps everything lean and consistent.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 331,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 329,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 304,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 302,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-heading font-bold\",\n                                            children: \"Modern Frontend\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 338,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-bold text-lg mb-2\",\n                                                            children: \"React (when needed)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 341,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-700\",\n                                                            children: \"For rich UIs, headless builds, or interactive components — used with intent.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 342,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 340,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-bold text-lg mb-2\",\n                                                            children: \"Headless WordPress\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 345,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-700\",\n                                                            children: \"REST-based content delivery with frameworks like Next.js or custom React frontends.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 346,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 344,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-bold text-lg mb-2\",\n                                                            children: \"Alpine.js\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 349,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-700\",\n                                                            children: \"When reactivity's needed but React would be overkill.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 350,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 348,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-bold text-lg mb-2\",\n                                                            children: \"Vite + ESBuild\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 353,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-700\",\n                                                            children: \"Fast bundling, instant reloads, and smooth local development.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 354,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 352,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 339,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-heading font-bold pt-8\",\n                                            children: \"Tooling & Workflow\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 358,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-bold text-lg mb-2\",\n                                                            children: \"Composer / NPM / Yarn\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 361,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-700\",\n                                                            children: \"Proper package management — backend and frontend.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 362,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 360,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-bold text-lg mb-2\",\n                                                            children: \"PostCSS\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 365,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-700\",\n                                                            children: \"Tailwind config, purging, prefixing, and build tweaks.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 366,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 364,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-bold text-lg mb-2\",\n                                                            children: \"Git + GitHub\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 369,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-700\",\n                                                            children: \"Everything versioned, reviewed, and documented.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 370,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 368,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-bold text-lg mb-2\",\n                                                            children: \"Figma\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 373,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-700\",\n                                                            children: \"Used for design systems, wireframes, and handoff.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 374,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 372,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-bold text-lg mb-2\",\n                                                            children: \"Markdown + Docs\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 377,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-700\",\n                                                            children: \"Clean documentation for devs and clients.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 378,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 376,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 359,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 337,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-heading font-bold\",\n                                            children: \"Extended Capabilities\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 385,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-bold text-lg mb-2\",\n                                                            children: \"Next.js\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 388,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-700\",\n                                                            children: \"Modern React frontend when needed\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 389,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 387,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-bold text-lg mb-2\",\n                                                            children: \"MySQL\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 392,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-700\",\n                                                            children: \"Standard WordPress data layer\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 393,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 391,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-bold text-lg mb-2\",\n                                                            children: \"DigitalOcean / Vercel / Netlify\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 396,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-700\",\n                                                            children: \"Deployment options tailored to the stack\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 397,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 395,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-bold text-lg mb-2\",\n                                                            children: \"REST APIs\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 400,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-700\",\n                                                            children: \"For custom integrations, data, or decoupled setups\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 401,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 399,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 386,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 384,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 300,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 294,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 293,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"px-6 md:px-12 lg:px-24 py-24 md:py-32 bg-bauhaus-black text-bauhaus-white relative overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-4xl mx-auto text-center relative z-10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-display font-bold mb-6\",\n                                children: \"Got something worth building?\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 412,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl mb-12 leading-relaxed\",\n                                children: \"Let's make it real. We'll help you strip it down to what matters — and bring it to life.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 413,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                href: \"/contact\",\n                                className: \"btn-primary bg-brand-background text-bauhaus-black border-brand-background hover:bg-transparent hover:text-brand-background inline-block\",\n                                children: \"Start Your Project\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 416,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 411,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 opacity-10\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_compositions_OrganicComposition__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            variant: \"feature\",\n                            className: \"w-full h-full\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 423,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 422,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-8 left-8 opacity-60\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shapes_AnimatedShapes__WEBPACK_IMPORTED_MODULE_3__.AnimatedSoftGrid, {\n                                    className: \"w-32 h-32 text-black\",\n                                    animationPreset: \"drift\",\n                                    animationIndex: 41\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 427,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shapes_AnimatedShapes__WEBPACK_IMPORTED_MODULE_3__.AnimatedSoftCircle, {\n                                    size: \"xl\",\n                                    color: \"red\",\n                                    className: \"absolute top-4 left-4 w-20 h-20\",\n                                    animationPreset: \"energetic\",\n                                    animationIndex: 42\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 428,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 426,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 425,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-8 right-8 opacity-60\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shapes_AnimatedShapes__WEBPACK_IMPORTED_MODULE_3__.AnimatedSoftGrid, {\n                                    className: \"w-28 h-28 text-black\",\n                                    animationPreset: \"float\",\n                                    animationIndex: 43\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 433,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shapes_AnimatedShapes__WEBPACK_IMPORTED_MODULE_3__.AnimatedRoundedRectangle, {\n                                    width: \"xl\",\n                                    height: \"lg\",\n                                    color: \"yellow\",\n                                    className: \"absolute top-3 left-3 w-18 h-14\",\n                                    animationPreset: \"flowing\",\n                                    animationIndex: 44\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 434,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 432,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 431,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 410,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/page.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/compositions/OrganicComposition.tsx":
/*!************************************************************!*\
  !*** ./src/components/compositions/OrganicComposition.tsx ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FeatureComposition: () => (/* binding */ FeatureComposition),\n/* harmony export */   HeroComposition: () => (/* binding */ HeroComposition),\n/* harmony export */   MinimalComposition: () => (/* binding */ MinimalComposition),\n/* harmony export */   \"default\": () => (/* binding */ OrganicComposition)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _shapes_RoundedShapes__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../shapes/RoundedShapes */ \"(rsc)/./src/components/shapes/RoundedShapes.tsx\");\n/* harmony import */ var _shapes_Circle__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../shapes/Circle */ \"(rsc)/./src/components/shapes/Circle.tsx\");\n/* harmony import */ var _shapes_Rectangle__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../shapes/Rectangle */ \"(rsc)/./src/components/shapes/Rectangle.tsx\");\n/* harmony import */ var _shapes_AnimatedShapes__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../shapes/AnimatedShapes */ \"(rsc)/./src/components/shapes/AnimatedShapes.tsx\");\n\n\n\n\n\n// Hero composition inspired by DSM and Homebase references\nfunction HeroComposition({ className = \"\" }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `relative ${className}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_shapes_AnimatedShapes__WEBPACK_IMPORTED_MODULE_4__.AnimatedSoftGrid, {\n                className: \"text-black h-full\",\n                opacity: \"hero\",\n                animationPreset: \"subtle\",\n                animationIndex: 0\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\compositions\\\\OrganicComposition.tsx\",\n                lineNumber: 24,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-0 right-0 w-64 h-64 opacity-80\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_shapes_AnimatedShapes__WEBPACK_IMPORTED_MODULE_4__.AnimatedHalfCircle, {\n                    color: \"blue\",\n                    direction: \"right\",\n                    className: \"w-full h-full\",\n                    animationPreset: \"gentle\",\n                    animationIndex: 1\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\compositions\\\\OrganicComposition.tsx\",\n                    lineNumber: 28,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\compositions\\\\OrganicComposition.tsx\",\n                lineNumber: 27,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute bottom-0 left-0 w-48 h-48\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_shapes_AnimatedShapes__WEBPACK_IMPORTED_MODULE_4__.AnimatedQuarterCircle, {\n                    color: \"yellow\",\n                    corner: \"bottom-left\",\n                    className: \"w-full h-full\",\n                    animationPreset: \"flowing\",\n                    animationIndex: 2\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\compositions\\\\OrganicComposition.tsx\",\n                    lineNumber: 38,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\compositions\\\\OrganicComposition.tsx\",\n                lineNumber: 37,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-1/4 left-1/3 w-24 h-24 opacity-90\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_shapes_AnimatedShapes__WEBPACK_IMPORTED_MODULE_4__.AnimatedBlob, {\n                    color: \"red\",\n                    className: \"w-full h-full\",\n                    animationPreset: \"dynamic\",\n                    animationIndex: 3\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\compositions\\\\OrganicComposition.tsx\",\n                    lineNumber: 49,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\compositions\\\\OrganicComposition.tsx\",\n                lineNumber: 48,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-1/2 right-1/4 w-20 h-16\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_shapes_AnimatedShapes__WEBPACK_IMPORTED_MODULE_4__.AnimatedSoftCircle, {\n                    size: \"lg\",\n                    color: \"yellow\",\n                    className: \"h-full\",\n                    animationPreset: \"energetic\",\n                    animationIndex: 4\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\compositions\\\\OrganicComposition.tsx\",\n                    lineNumber: 58,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\compositions\\\\OrganicComposition.tsx\",\n                lineNumber: 57,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute bottom-1/3 left-1/4 w-16 h-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_shapes_AnimatedShapes__WEBPACK_IMPORTED_MODULE_4__.AnimatedPill, {\n                    color: \"black\",\n                    className: \"w-full h-full\",\n                    animationPreset: \"horizontal\",\n                    animationIndex: 5\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\compositions\\\\OrganicComposition.tsx\",\n                    lineNumber: 69,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\compositions\\\\OrganicComposition.tsx\",\n                lineNumber: 68,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-3/4 right-1/3 w-12 h-12\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_shapes_AnimatedShapes__WEBPACK_IMPORTED_MODULE_4__.AnimatedRoundedRect, {\n                    color: \"blue\",\n                    className: \"w-full h-full\",\n                    animationPreset: \"pulse\",\n                    animationIndex: 6\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\compositions\\\\OrganicComposition.tsx\",\n                    lineNumber: 78,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\compositions\\\\OrganicComposition.tsx\",\n                lineNumber: 77,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\compositions\\\\OrganicComposition.tsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, this);\n}\n// Feature composition inspired by Collabo reference\nfunction FeatureComposition({ className = \"\" }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `relative ${className}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center space-x-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_shapes_Circle__WEBPACK_IMPORTED_MODULE_2__.SoftCircle, {\n                        size: \"xl\",\n                        color: \"blue\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\compositions\\\\OrganicComposition.tsx\",\n                        lineNumber: 95,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_shapes_Rectangle__WEBPACK_IMPORTED_MODULE_3__.RoundedRectangle, {\n                                width: \"lg\",\n                                height: \"xl\",\n                                color: \"yellow\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\compositions\\\\OrganicComposition.tsx\",\n                                lineNumber: 98,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_shapes_Circle__WEBPACK_IMPORTED_MODULE_2__.SoftCircle, {\n                                size: \"lg\",\n                                color: \"black\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\compositions\\\\OrganicComposition.tsx\",\n                                lineNumber: 99,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\compositions\\\\OrganicComposition.tsx\",\n                        lineNumber: 97,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_shapes_RoundedShapes__WEBPACK_IMPORTED_MODULE_1__.QuarterCircle, {\n                        color: \"red\",\n                        corner: \"top-right\",\n                        className: \"w-32 h-32\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\compositions\\\\OrganicComposition.tsx\",\n                        lineNumber: 102,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\compositions\\\\OrganicComposition.tsx\",\n                lineNumber: 94,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_shapes_RoundedShapes__WEBPACK_IMPORTED_MODULE_1__.HalfCircle, {\n                color: \"blue\",\n                direction: \"left\",\n                className: \"absolute -top-8 -right-8 w-24 h-24 opacity-60\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\compositions\\\\OrganicComposition.tsx\",\n                lineNumber: 110,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\compositions\\\\OrganicComposition.tsx\",\n        lineNumber: 92,\n        columnNumber: 5\n    }, this);\n}\n// Minimal composition inspired by Horizon reference\nfunction MinimalComposition({ className = \"\" }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `relative ${className}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-3 gap-8 items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_shapes_Circle__WEBPACK_IMPORTED_MODULE_2__.SoftCircle, {\n                                size: \"md\",\n                                color: \"black\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\compositions\\\\OrganicComposition.tsx\",\n                                lineNumber: 126,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_shapes_RoundedShapes__WEBPACK_IMPORTED_MODULE_1__.Pill, {\n                                color: \"yellow\",\n                                className: \"w-20 h-8\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\compositions\\\\OrganicComposition.tsx\",\n                                lineNumber: 127,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\compositions\\\\OrganicComposition.tsx\",\n                        lineNumber: 125,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_shapes_RoundedShapes__WEBPACK_IMPORTED_MODULE_1__.RoundedRect, {\n                                color: \"red\",\n                                className: \"w-24 h-24\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\compositions\\\\OrganicComposition.tsx\",\n                                lineNumber: 131,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_shapes_Circle__WEBPACK_IMPORTED_MODULE_2__.SoftCircle, {\n                                size: \"sm\",\n                                color: \"black\",\n                                className: \"absolute -bottom-2 -right-2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\compositions\\\\OrganicComposition.tsx\",\n                                lineNumber: 132,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\compositions\\\\OrganicComposition.tsx\",\n                        lineNumber: 130,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_shapes_RoundedShapes__WEBPACK_IMPORTED_MODULE_1__.Blob, {\n                        color: \"blue\",\n                        className: \"w-20 h-20\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\compositions\\\\OrganicComposition.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\compositions\\\\OrganicComposition.tsx\",\n                lineNumber: 124,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"absolute inset-0 w-full h-full pointer-events-none opacity-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    d: \"M 50 50 Q 150 100 250 50\",\n                    stroke: \"currentColor\",\n                    strokeWidth: \"1\",\n                    fill: \"none\",\n                    className: \"text-black\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\compositions\\\\OrganicComposition.tsx\",\n                    lineNumber: 144,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\compositions\\\\OrganicComposition.tsx\",\n                lineNumber: 143,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\compositions\\\\OrganicComposition.tsx\",\n        lineNumber: 122,\n        columnNumber: 5\n    }, this);\n}\n// Main component that switches between variants\nfunction OrganicComposition({ variant = \"hero\", className = \"\" }) {\n    switch(variant){\n        case \"hero\":\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(HeroComposition, {\n                className: className\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\compositions\\\\OrganicComposition.tsx\",\n                lineNumber: 163,\n                columnNumber: 14\n            }, this);\n        case \"feature\":\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeatureComposition, {\n                className: className\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\compositions\\\\OrganicComposition.tsx\",\n                lineNumber: 165,\n                columnNumber: 14\n            }, this);\n        case \"minimal\":\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MinimalComposition, {\n                className: className\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\compositions\\\\OrganicComposition.tsx\",\n                lineNumber: 167,\n                columnNumber: 14\n            }, this);\n        default:\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(HeroComposition, {\n                className: className\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\compositions\\\\OrganicComposition.tsx\",\n                lineNumber: 169,\n                columnNumber: 14\n            }, this);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/components/compositions/OrganicComposition.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/layout/Footer.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Footer.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Footer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(rsc)/./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction Footer() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"w-full py-12 px-6 md:px-12 lg:px-24 border-t border-bauhaus-black\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col md:flex-row justify-between items-start md:items-center space-y-6 md:space-y-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    src: \"/images/logo.png\",\n                                    alt: \"Navhaus\",\n                                    width: 100,\n                                    height: 32,\n                                    className: \"h-6 w-auto\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 10,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                lineNumber: 9,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-600\",\n                                children: \"What matters, made real.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                lineNumber: 18,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                        lineNumber: 8,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-right\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm font-medium mb-1\",\n                                children: \"Ready to build something?\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                lineNumber: 25,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"mailto:<EMAIL>\",\n                                className: \"text-sm text-bauhaus-red hover:underline\",\n                                children: \"<EMAIL>\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                lineNumber: 28,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                        lineNumber: 24,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                lineNumber: 6,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-8 pt-6 border-t border-gray-200 text-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-xs text-gray-500\",\n                    children: [\n                        \"\\xa9 \",\n                        new Date().getFullYear(),\n                        \" Navhaus. All rights reserved.\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                    lineNumber: 39,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                lineNumber: 38,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/components/layout/Footer.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/layout/Header.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Header.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Projects\navhaus\src\components\layout\Header.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/components/layout/PageWrapper.tsx":
/*!***********************************************!*\
  !*** ./src/components/layout/PageWrapper.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PageWrapper)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Header__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Header */ \"(rsc)/./src/components/layout/Header.tsx\");\n/* harmony import */ var _Footer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Footer */ \"(rsc)/./src/components/layout/Footer.tsx\");\n\n\n\nfunction PageWrapper({ children, className = \"\" }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Header__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\PageWrapper.tsx\",\n                lineNumber: 12,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: `flex-1 ${className}`,\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\PageWrapper.tsx\",\n                lineNumber: 13,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Footer__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\PageWrapper.tsx\",\n                lineNumber: 16,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\PageWrapper.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvY29tcG9uZW50cy9sYXlvdXQvUGFnZVdyYXBwZXIudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUE2QjtBQUNBO0FBT2QsU0FBU0UsWUFBWSxFQUFFQyxRQUFRLEVBQUVDLFlBQVksRUFBRSxFQUFvQjtJQUNoRixxQkFDRSw4REFBQ0M7UUFBSUQsV0FBVTs7MEJBQ2IsOERBQUNKLCtDQUFNQTs7Ozs7MEJBQ1AsOERBQUNNO2dCQUFLRixXQUFXLENBQUMsT0FBTyxFQUFFQSxVQUFVLENBQUM7MEJBQ25DRDs7Ozs7OzBCQUVILDhEQUFDRiwrQ0FBTUE7Ozs7Ozs7Ozs7O0FBR2IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uYXZoYXVzLXdlYnNpdGUvLi9zcmMvY29tcG9uZW50cy9sYXlvdXQvUGFnZVdyYXBwZXIudHN4PzE2N2QiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IEhlYWRlciBmcm9tICcuL0hlYWRlcidcbmltcG9ydCBGb290ZXIgZnJvbSAnLi9Gb290ZXInXG5cbmludGVyZmFjZSBQYWdlV3JhcHBlclByb3BzIHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZVxuICBjbGFzc05hbWU/OiBzdHJpbmdcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUGFnZVdyYXBwZXIoeyBjaGlsZHJlbiwgY2xhc3NOYW1lID0gJycgfTogUGFnZVdyYXBwZXJQcm9wcykge1xuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIGZsZXggZmxleC1jb2xcIj5cbiAgICAgIDxIZWFkZXIgLz5cbiAgICAgIDxtYWluIGNsYXNzTmFtZT17YGZsZXgtMSAke2NsYXNzTmFtZX1gfT5cbiAgICAgICAge2NoaWxkcmVufVxuICAgICAgPC9tYWluPlxuICAgICAgPEZvb3RlciAvPlxuICAgIDwvZGl2PlxuICApXG59XG4iXSwibmFtZXMiOlsiSGVhZGVyIiwiRm9vdGVyIiwiUGFnZVdyYXBwZXIiLCJjaGlsZHJlbiIsImNsYXNzTmFtZSIsImRpdiIsIm1haW4iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/components/layout/PageWrapper.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/shapes/AnimatedShapes.tsx":
/*!**************************************************!*\
  !*** ./src/components/shapes/AnimatedShapes.tsx ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AnimatedBlob: () => (/* binding */ e9),
/* harmony export */   AnimatedCircle: () => (/* binding */ e0),
/* harmony export */   AnimatedHalfCircle: () => (/* binding */ e5),
/* harmony export */   AnimatedPill: () => (/* binding */ e8),
/* harmony export */   AnimatedQuarterCircle: () => (/* binding */ e10),
/* harmony export */   AnimatedRectangle: () => (/* binding */ e2),
/* harmony export */   AnimatedRoundedRect: () => (/* binding */ e7),
/* harmony export */   AnimatedRoundedRectangle: () => (/* binding */ e3),
/* harmony export */   AnimatedSoftCircle: () => (/* binding */ e1),
/* harmony export */   AnimatedSoftGrid: () => (/* binding */ e6),
/* harmony export */   AnimatedTriangle: () => (/* binding */ e4)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Projects\navhaus\src\components\shapes\AnimatedShapes.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Projects\navhaus\src\components\shapes\AnimatedShapes.tsx#AnimatedCircle`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Projects\navhaus\src\components\shapes\AnimatedShapes.tsx#AnimatedSoftCircle`);

const e2 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Projects\navhaus\src\components\shapes\AnimatedShapes.tsx#AnimatedRectangle`);

const e3 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Projects\navhaus\src\components\shapes\AnimatedShapes.tsx#AnimatedRoundedRectangle`);

const e4 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Projects\navhaus\src\components\shapes\AnimatedShapes.tsx#AnimatedTriangle`);

const e5 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Projects\navhaus\src\components\shapes\AnimatedShapes.tsx#AnimatedHalfCircle`);

const e6 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Projects\navhaus\src\components\shapes\AnimatedShapes.tsx#AnimatedSoftGrid`);

const e7 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Projects\navhaus\src\components\shapes\AnimatedShapes.tsx#AnimatedRoundedRect`);

const e8 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Projects\navhaus\src\components\shapes\AnimatedShapes.tsx#AnimatedPill`);

const e9 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Projects\navhaus\src\components\shapes\AnimatedShapes.tsx#AnimatedBlob`);

const e10 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Projects\navhaus\src\components\shapes\AnimatedShapes.tsx#AnimatedQuarterCircle`);


/***/ }),

/***/ "(rsc)/./src/components/shapes/Circle.tsx":
/*!******************************************!*\
  !*** ./src/components/shapes/Circle.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SoftCircle: () => (/* binding */ SoftCircle),\n/* harmony export */   \"default\": () => (/* binding */ Circle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nconst sizeClasses = {\n    sm: \"w-8 h-8\",\n    md: \"w-16 h-16\",\n    lg: \"w-24 h-24\",\n    xl: \"w-32 h-32\"\n};\nconst colorClasses = {\n    red: \"bg-brand-red\",\n    yellow: \"bg-brand-yellow\",\n    blue: \"bg-brand-blue\",\n    black: \"bg-black\",\n    white: \"bg-white border border-black\"\n};\nfunction Circle({ size = \"md\", color = \"red\", className = \"\" }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `rounded-full ${sizeClasses[size]} ${colorClasses[color]} ${className}`\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\Circle.tsx\",\n        lineNumber: 28,\n        columnNumber: 5\n    }, this);\n}\n// Soft Circle with organic feel (inspired by logo roundedness)\nfunction SoftCircle({ size = \"md\", color = \"red\", className = \"\" }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `rounded-full ${sizeClasses[size]} ${colorClasses[color]} ${className}`,\n        style: {\n            filter: \"blur(0.5px)\"\n        }\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\Circle.tsx\",\n        lineNumber: 41,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/components/shapes/Circle.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/shapes/Rectangle.tsx":
/*!*********************************************!*\
  !*** ./src/components/shapes/Rectangle.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PillRectangle: () => (/* binding */ PillRectangle),\n/* harmony export */   RoundedRectangle: () => (/* binding */ RoundedRectangle),\n/* harmony export */   \"default\": () => (/* binding */ Rectangle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nconst widthClasses = {\n    sm: \"w-12\",\n    md: \"w-24\",\n    lg: \"w-32\",\n    xl: \"w-48\"\n};\nconst heightClasses = {\n    sm: \"h-8\",\n    md: \"h-16\",\n    lg: \"h-24\",\n    xl: \"h-32\"\n};\nconst colorClasses = {\n    red: \"bg-brand-red\",\n    yellow: \"bg-brand-yellow\",\n    blue: \"bg-brand-blue\",\n    black: \"bg-black\",\n    white: \"bg-white border border-black\"\n};\nfunction Rectangle({ width = \"md\", height = \"md\", color = \"blue\", className = \"\" }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `${widthClasses[width]} ${heightClasses[height]} ${colorClasses[color]} ${className}`\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\Rectangle.tsx\",\n        lineNumber: 37,\n        columnNumber: 5\n    }, this);\n}\n// Rounded Rectangle (inspired by logo's soft corners)\nfunction RoundedRectangle({ width = \"md\", height = \"md\", color = \"blue\", className = \"\" }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `rounded-3xl ${widthClasses[width]} ${heightClasses[height]} ${colorClasses[color]} ${className}`\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\Rectangle.tsx\",\n        lineNumber: 51,\n        columnNumber: 5\n    }, this);\n}\n// Pill shape (very rounded rectangle)\nfunction PillRectangle({ width = \"md\", height = \"md\", color = \"yellow\", className = \"\" }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `rounded-full ${widthClasses[width]} ${heightClasses[height]} ${colorClasses[color]} ${className}`\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\Rectangle.tsx\",\n        lineNumber: 65,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/components/shapes/Rectangle.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/shapes/RoundedShapes.tsx":
/*!*************************************************!*\
  !*** ./src/components/shapes/RoundedShapes.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Blob: () => (/* binding */ Blob),\n/* harmony export */   HalfCircle: () => (/* binding */ HalfCircle),\n/* harmony export */   Pill: () => (/* binding */ Pill),\n/* harmony export */   QuarterCircle: () => (/* binding */ QuarterCircle),\n/* harmony export */   RoundedRect: () => (/* binding */ RoundedRect),\n/* harmony export */   RoundedSquareWithCutout: () => (/* binding */ RoundedSquareWithCutout),\n/* harmony export */   SoftGrid: () => (/* binding */ SoftGrid)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\n// Rounded Rectangle with soft corners\nfunction RoundedRect({ className = \"\", color = \"blue\" }) {\n    const colorClasses = {\n        red: \"bg-brand-red\",\n        yellow: \"bg-brand-yellow\",\n        blue: \"bg-brand-blue\",\n        black: \"bg-black\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `${colorClasses[color]} rounded-3xl ${className}`\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\RoundedShapes.tsx\",\n        lineNumber: 16,\n        columnNumber: 5\n    }, this);\n}\n// Pill shape (very rounded rectangle)\nfunction Pill({ className = \"\", color = \"yellow\" }) {\n    const colorClasses = {\n        red: \"bg-brand-red\",\n        yellow: \"bg-brand-yellow\",\n        blue: \"bg-brand-blue\",\n        black: \"bg-black\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `${colorClasses[color]} rounded-full ${className}`\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\RoundedShapes.tsx\",\n        lineNumber: 30,\n        columnNumber: 5\n    }, this);\n}\n// Organic blob shape using CSS\nfunction Blob({ className = \"\", color = \"red\" }) {\n    const colorClasses = {\n        red: \"bg-brand-red\",\n        yellow: \"bg-brand-yellow\",\n        blue: \"bg-brand-blue\",\n        black: \"bg-black\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `${colorClasses[color]} ${className}`,\n        style: {\n            borderRadius: \"60% 40% 30% 70% / 60% 30% 70% 40%\"\n        }\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\RoundedShapes.tsx\",\n        lineNumber: 44,\n        columnNumber: 5\n    }, this);\n}\n// Half circle (like in your references)\nfunction HalfCircle({ className = \"\", color = \"blue\", direction = \"right\" }) {\n    const colorClasses = {\n        red: \"bg-brand-red\",\n        yellow: \"bg-brand-yellow\",\n        blue: \"bg-brand-blue\",\n        black: \"bg-black\"\n    };\n    const directionClasses = {\n        right: \"rounded-l-full\",\n        left: \"rounded-r-full\",\n        top: \"rounded-b-full\",\n        bottom: \"rounded-t-full\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `${colorClasses[color]} ${directionClasses[direction]} ${className}`\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\RoundedShapes.tsx\",\n        lineNumber: 70,\n        columnNumber: 5\n    }, this);\n}\n// Quarter circle\nfunction QuarterCircle({ className = \"\", color = \"yellow\", corner = \"top-left\" }) {\n    const colorClasses = {\n        red: \"bg-brand-red\",\n        yellow: \"bg-brand-yellow\",\n        blue: \"bg-brand-blue\",\n        black: \"bg-black\"\n    };\n    const cornerClasses = {\n        \"top-left\": \"rounded-br-full\",\n        \"top-right\": \"rounded-bl-full\",\n        \"bottom-left\": \"rounded-tr-full\",\n        \"bottom-right\": \"rounded-tl-full\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `${colorClasses[color]} ${cornerClasses[corner]} ${className}`\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\RoundedShapes.tsx\",\n        lineNumber: 91,\n        columnNumber: 5\n    }, this);\n}\n// Rounded square with cutout (inspired by your logo)\nfunction RoundedSquareWithCutout({ className = \"\", color = \"black\" }) {\n    const colorClasses = {\n        red: \"bg-brand-red\",\n        yellow: \"bg-brand-yellow\",\n        blue: \"bg-brand-blue\",\n        black: \"bg-black\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `relative ${className}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `${colorClasses[color]} rounded-3xl w-full h-full`\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\RoundedShapes.tsx\",\n                lineNumber: 106,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-brand-background rounded-full w-1/2 h-1/2\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\RoundedShapes.tsx\",\n                lineNumber: 107,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\RoundedShapes.tsx\",\n        lineNumber: 105,\n        columnNumber: 5\n    }, this);\n}\n// Soft grid overlay (like in the DSM reference)\nfunction SoftGrid({ className = \"\", opacity = \"default\" }) {\n    const opacityClass = opacity === \"hero\" ? \"opacity-40\" : \"opacity-20\";\n    const strokeOpacity = opacity === \"hero\" ? \"0.5\" : \"0.3\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `absolute inset-0 ${opacityClass} ${className}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            width: \"100%\",\n            height: \"100%\",\n            className: \"w-full h-full\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"defs\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pattern\", {\n                        id: \"grid\",\n                        width: \"40\",\n                        height: \"40\",\n                        patternUnits: \"userSpaceOnUse\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            d: \"M 40 0 L 0 0 0 40\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            strokeWidth: \"1\",\n                            opacity: strokeOpacity\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\RoundedShapes.tsx\",\n                            lineNumber: 122,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\RoundedShapes.tsx\",\n                        lineNumber: 121,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\RoundedShapes.tsx\",\n                    lineNumber: 120,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                    width: \"100%\",\n                    height: \"100%\",\n                    fill: \"url(#grid)\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\RoundedShapes.tsx\",\n                    lineNumber: 125,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\RoundedShapes.tsx\",\n            lineNumber: 119,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\RoundedShapes.tsx\",\n        lineNumber: 118,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/components/shapes/RoundedShapes.tsx\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CSaud%5CProjects%5Cnavhaus%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CSaud%5CProjects%5Cnavhaus&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();