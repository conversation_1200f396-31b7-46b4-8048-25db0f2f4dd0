import PageWrapper from '@/components/layout/PageWrapper'
import OrganicComposition from '@/components/compositions/OrganicComposition'
import {
  AnimatedSoftCircle,
  AnimatedRoundedRectangle,
  AnimatedTriangle,
  AnimatedSoftGrid
} from '@/components/shapes/AnimatedShapes'
import Link from 'next/link'

export default function Home() {
  return (
    <PageWrapper>
      {/* Hero Section */}
      <section className="relative px-6 md:px-12 lg:px-24 pt-16 pb-16 overflow-hidden">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-16 items-center">
            {/* Hero Content */}
            <div className="space-y-8">
              <h1 className="text-hero font-bold leading-tight">
                What matters, made real.
              </h1>
              <p className="text-xl md:text-2xl leading-relaxed text-gray-700 max-w-lg">
                Navhaus is a design and development studio that builds bold, efficient, 
                and meaningful digital experiences — nothing more, nothing less.
              </p>
              <Link href="/contact" className="btn-red inline-block">
                Start Your Project
              </Link>
            </div>
            
            {/* Organic Geometric Composition */}
            <div className="relative h-96 lg:h-[500px]">
              <OrganicComposition variant="hero" className="w-full h-full" />
            </div>
          </div>
        </div>
      </section>

      {/* How We Work Section */}
      <section className="px-6 md:px-12 lg:px-24 py-8 bg-brand-background">
        <div className="max-w-7xl mx-auto">
          <h2 className="text-display font-bold text-center mb-16">How We Work</h2>

          <div className="space-y-24">
            {/* Process 1 - Left aligned */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <div className="relative h-64 w-full">
                {/* Grid background */}
                <div className="absolute inset-0">
                  <AnimatedSoftGrid className="w-full h-full text-black" opacity="hero" animationPreset="subtle" animationIndex={0} />
                </div>
                {/* Main shape */}
                <div className="relative flex justify-center lg:justify-start items-center h-full">
                  <AnimatedSoftCircle size="xl" color="blue" className="w-32 h-32" animationPreset="gentle" animationIndex={1} />
                </div>
                {/* Dense decorative elements */}
                <div className="absolute top-4 right-8 opacity-80">
                  <AnimatedRoundedRectangle width="md" height="sm" color="yellow" className="w-12 h-8" animationPreset="flowing" animationIndex={2} />
                </div>
                <div className="absolute bottom-8 left-16 opacity-80">
                  <AnimatedSoftCircle size="md" color="red" className="w-10 h-10" animationPreset="gentle" animationIndex={3} />
                </div>
                <div className="absolute top-1/2 right-4 opacity-70">
                  <AnimatedTriangle size="md" color="red" direction="up" className="w-8 h-8" animationPreset="energetic" animationIndex={4} />
                </div>
                <div className="absolute top-8 left-8 opacity-60">
                  <AnimatedRoundedRectangle width="sm" height="sm" color="red" className="w-6 h-3" animationPreset="drift" animationIndex={5} />
                </div>
                <div className="absolute bottom-4 right-16 opacity-70">
                  <AnimatedSoftCircle size="sm" color="yellow" className="w-6 h-6" animationPreset="float" animationIndex={6} />
                </div>
                <div className="absolute top-16 right-20 opacity-50">
                  <AnimatedTriangle size="sm" color="blue" direction="up" className="w-4 h-4" animationPreset="pulse" animationIndex={7} />
                </div>
                <div className="absolute bottom-16 left-4 opacity-60">
                  <AnimatedRoundedRectangle width="sm" height="sm" color="blue" className="w-4 h-4" animationPreset="flowing" animationIndex={8} />
                </div>
                <div className="absolute top-1/3 left-1/3 opacity-50">
                  <AnimatedSoftCircle size="sm" color="yellow" className="w-4 h-4" animationPreset="drift" animationIndex={9} />
                </div>
              </div>
              <div className="space-y-6 text-center lg:text-left">
                <h3 className="text-heading font-bold">Listen</h3>
                <p className="text-body text-gray-700 leading-relaxed text-lg">
                  We start by understanding exactly what you're trying to do — and more importantly, why.
                </p>
              </div>
            </div>

            {/* Process 2 - Right aligned */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <div className="space-y-6 text-center lg:text-right lg:order-1">
                <h3 className="text-heading font-bold">Design</h3>
                <p className="text-body text-gray-700 leading-relaxed text-lg">
                  We strip away noise, leaving only what needs to be there. Everything has a reason.
                </p>
              </div>
              <div className="relative h-64 w-full lg:order-2">
                {/* Grid background */}
                <div className="absolute inset-0">
                  <AnimatedSoftGrid className="w-full h-full text-black" opacity="hero" animationPreset="subtle" animationIndex={10} />
                </div>
                {/* Main shape */}
                <div className="relative flex justify-center lg:justify-end items-center h-full">
                  <AnimatedRoundedRectangle width="xl" height="xl" color="yellow" className="w-32 h-24" animationPreset="gentle" animationIndex={11} />
                </div>
                {/* Dense decorative elements */}
                <div className="absolute top-6 left-8 opacity-80">
                  <AnimatedSoftCircle size="lg" color="blue" className="w-14 h-14" animationPreset="energetic" animationIndex={12} />
                </div>
                <div className="absolute bottom-4 right-20 opacity-80">
                  <AnimatedTriangle size="md" color="red" direction="up" className="w-8 h-8" animationPreset="dynamic" animationIndex={13} />
                </div>
                <div className="absolute top-1/3 left-4 opacity-70">
                  <AnimatedRoundedRectangle width="sm" height="sm" color="blue" className="w-6 h-6" animationPreset="pulse" animationIndex={14} />
                </div>
                <div className="absolute bottom-1/3 left-1/3 opacity-70">
                  <AnimatedSoftCircle size="sm" color="red" className="w-6 h-6" animationPreset="flowing" animationIndex={15} />
                </div>
                <div className="absolute top-4 right-4 opacity-60">
                  <AnimatedTriangle size="sm" color="blue" direction="up" className="w-5 h-5" animationPreset="drift" animationIndex={16} />
                </div>
                <div className="absolute bottom-8 left-20 opacity-60">
                  <AnimatedRoundedRectangle width="sm" height="md" color="red" className="w-3 h-8" animationPreset="gentle" animationIndex={17} />
                </div>
                <div className="absolute top-20 left-16 opacity-50">
                  <AnimatedSoftCircle size="sm" color="yellow" className="w-4 h-4" animationPreset="float" animationIndex={18} />
                </div>
                <div className="absolute bottom-20 right-8 opacity-60">
                  <AnimatedTriangle size="sm" color="yellow" direction="up" className="w-3 h-3" animationPreset="pulse" animationIndex={19} />
                </div>
                <div className="absolute top-1/2 left-1/2 opacity-50">
                  <AnimatedRoundedRectangle width="sm" height="sm" color="blue" className="w-3 h-3" animationPreset="drift" animationIndex={20} />
                </div>
              </div>
            </div>

            {/* Process 3 - Left aligned */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <div className="relative h-64 w-full">
                {/* Grid background */}
                <div className="absolute inset-0">
                  <AnimatedSoftGrid className="w-full h-full text-black" opacity="hero" animationPreset="subtle" animationIndex={21} />
                </div>
                {/* Building blocks composition */}
                <div className="relative flex justify-center lg:justify-start items-center h-full">
                  {/* Base foundation - larger and more prominent */}
                  <div className="absolute bottom-12">
                    <AnimatedRoundedRectangle width="xl" height="lg" color="red" className="w-40 h-12" animationPreset="flowing" animationIndex={22} />
                  </div>
                  {/* Middle layer */}
                  <div className="absolute bottom-20 left-4">
                    <AnimatedRoundedRectangle width="xl" height="lg" color="blue" className="w-32 h-12" animationPreset="energetic" animationIndex={23} />
                  </div>
                  {/* Top layer */}
                  <div className="absolute bottom-28 left-8">
                    <AnimatedRoundedRectangle width="lg" height="lg" color="yellow" className="w-24 h-12" animationPreset="pulse" animationIndex={24} />
                  </div>
                  {/* Connecting elements - larger and more visible */}
                  <div className="absolute bottom-16 right-4 opacity-80">
                    <AnimatedSoftCircle size="md" color="yellow" className="w-10 h-10" animationPreset="dynamic" animationIndex={25} />
                  </div>
                  <div className="absolute bottom-24 right-8 opacity-80">
                    <AnimatedSoftCircle size="sm" color="red" className="w-6 h-6" animationPreset="drift" animationIndex={26} />
                  </div>
                  <div className="absolute bottom-32 right-12 opacity-70">
                    <AnimatedTriangle size="sm" color="blue" direction="up" className="w-6 h-6" animationPreset="energetic" animationIndex={27} />
                  </div>
                  {/* Dense decorative elements */}
                  <div className="absolute top-8 right-4 opacity-70">
                    <AnimatedTriangle size="md" color="blue" direction="up" className="w-8 h-8" animationPreset="flowing" animationIndex={28} />
                  </div>
                  <div className="absolute top-16 left-4 opacity-60">
                    <AnimatedRoundedRectangle width="sm" height="sm" color="yellow" className="w-6 h-6" animationPreset="gentle" animationIndex={29} />
                  </div>
                  <div className="absolute top-4 left-12 opacity-60">
                    <AnimatedSoftCircle size="sm" color="red" className="w-6 h-6" animationPreset="float" animationIndex={30} />
                  </div>
                  <div className="absolute top-12 right-16 opacity-50">
                    <AnimatedRoundedRectangle width="sm" height="md" color="red" className="w-3 h-6" animationPreset="pulse" animationIndex={31} />
                  </div>
                  <div className="absolute top-20 left-16 opacity-50">
                    <AnimatedTriangle size="sm" color="yellow" direction="up" className="w-4 h-4" animationPreset="drift" animationIndex={32} />
                  </div>
                  <div className="absolute top-6 left-20 opacity-40">
                    <AnimatedSoftCircle size="sm" color="blue" className="w-4 h-4" animationPreset="float" animationIndex={33} />
                  </div>
                  <div className="absolute bottom-8 left-20 opacity-60">
                    <AnimatedRoundedRectangle width="sm" height="sm" color="yellow" className="w-4 h-4" animationPreset="flowing" animationIndex={34} />
                  </div>
                </div>
              </div>
              <div className="space-y-6 text-center lg:text-left">
                <h3 className="text-heading font-bold">Build</h3>
                <p className="text-body text-gray-700 leading-relaxed text-lg">
                  We ship clean code and scalable systems. Fast. Reliable. Yours to own.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Things We Love Building Section */}
      <section className="relative px-6 md:px-12 lg:px-24 py-16 md:py-24 overflow-hidden bg-bauhaus-black">
        <div className="max-w-7xl mx-auto relative z-10">
          <h2 className="text-display font-bold text-center mb-8 text-brand-background">Things We Love Building</h2>
          <p className="text-xl text-center text-gray-300 mb-16 max-w-3xl mx-auto">
            No big case studies (yet), but here's what gets us fired up.
          </p>

          {/* Asymmetric Mosaic Layout */}
          <div className="grid grid-cols-12 gap-6 h-[600px] md:h-[700px]">
            {/* Large Feature Card - Marketing Sites */}
            <div className="col-span-12 md:col-span-7 lg:col-span-8 relative group">
              <div className="h-full bg-brand-background border-3 border-brand-background rounded-3xl p-8 md:p-12 flex flex-col justify-between relative overflow-hidden">
                {/* Background grid */}
                <div className="absolute inset-0 opacity-40">
                  <AnimatedSoftGrid className="w-full h-full text-black" opacity="default" animationPreset="subtle" animationIndex={35} />
                </div>

                {/* Content */}
                <div className="relative z-10">
                  <div className="inline-block bg-bauhaus-red text-white px-4 py-2 rounded-full text-sm font-bold mb-6">
                    PERFORMANCE
                  </div>
                  <h3 className="text-2xl md:text-3xl font-bold mb-4 text-bauhaus-black">High-performance marketing sites</h3>
                  <p className="text-lg text-gray-700 leading-relaxed max-w-lg">
                    Built to load fast, convert hard, and stay lean. Every element optimized for speed and results.
                  </p>
                </div>

                {/* Dynamic speed visualization */}
                <div className="absolute bottom-8 right-8 opacity-80">
                  <div className="relative">
                    <AnimatedSoftCircle size="xl" color="red" className="w-16 h-16" animationPreset="energetic" animationIndex={36} />
                    <div className="absolute -right-8 top-1/2 transform -translate-y-1/2">
                      <AnimatedRoundedRectangle width="lg" height="sm" color="yellow" className="w-12 h-2" animationPreset="flowing" animationIndex={37} />
                    </div>
                    <div className="absolute -right-12 top-1/2 transform -translate-y-1/2">
                      <AnimatedRoundedRectangle width="md" height="sm" color="yellow" className="w-8 h-2" animationPreset="flowing" animationIndex={38} />
                    </div>
                    <div className="absolute -right-16 top-1/2 transform -translate-y-1/2">
                      <AnimatedRoundedRectangle width="sm" height="sm" color="yellow" className="w-4 h-1" animationPreset="flowing" animationIndex={39} />
                    </div>
                  </div>
                </div>

                {/* Floating accent shapes */}
                <div className="absolute top-12 right-20 opacity-60">
                  <AnimatedTriangle size="sm" color="blue" direction="up" className="w-6 h-6" animationPreset="dynamic" animationIndex={40} />
                </div>
                <div className="absolute top-1/3 right-1/4 opacity-50">
                  <AnimatedSoftCircle size="sm" color="yellow" className="w-4 h-4" animationPreset="gentle" animationIndex={41} />
                </div>
              </div>
            </div>

            {/* Tall Card - Design Systems */}
            <div className="col-span-12 md:col-span-5 lg:col-span-4 relative">
              <div className="h-full bg-bauhaus-blue text-white border-3 border-bauhaus-blue rounded-3xl p-6 md:p-8 flex flex-col justify-between relative overflow-hidden">
                {/* Background grid */}
                <div className="absolute inset-0 opacity-30">
                  <AnimatedSoftGrid className="w-full h-full text-white" opacity="default" animationPreset="drift" animationIndex={42} />
                </div>

                {/* Content */}
                <div className="relative z-10">
                  <div className="inline-block bg-brand-background text-bauhaus-blue px-3 py-1 rounded-full text-xs font-bold mb-4">
                    SCALABLE
                  </div>
                  <h3 className="text-xl md:text-2xl font-bold mb-4">Design systems that scale</h3>
                  <p className="text-blue-100 leading-relaxed">
                    Modular components that grow with your needs. Interfaces that expand without chaos.
                  </p>
                </div>

                {/* Modular blocks visualization */}
                <div className="relative mt-8">
                  <div className="flex space-x-2 mb-2">
                    <AnimatedRoundedRectangle width="md" height="sm" color="yellow" className="w-8 h-6" animationPreset="gentle" animationIndex={43} />
                    <AnimatedRoundedRectangle width="md" height="sm" color="white" className="w-8 h-6" animationPreset="gentle" animationIndex={44} />
                  </div>
                  <div className="flex space-x-2 mb-2">
                    <AnimatedRoundedRectangle width="lg" height="sm" color="white" className="w-12 h-6" animationPreset="gentle" animationIndex={45} />
                    <AnimatedRoundedRectangle width="sm" height="sm" color="yellow" className="w-6 h-6" animationPreset="gentle" animationIndex={46} />
                  </div>
                  <div className="flex space-x-2">
                    <AnimatedRoundedRectangle width="sm" height="sm" color="yellow" className="w-6 h-6" animationPreset="gentle" animationIndex={47} />
                    <AnimatedRoundedRectangle width="md" height="sm" color="white" className="w-8 h-6" animationPreset="gentle" animationIndex={48} />
                    <AnimatedRoundedRectangle width="sm" height="sm" color="yellow" className="w-6 h-6" animationPreset="gentle" animationIndex={49} />
                  </div>
                </div>
              </div>
            </div>

            {/* Wide Card - Gutenberg Blocks */}
            <div className="col-span-12 md:col-span-8 relative">
              <div className="h-full bg-bauhaus-yellow text-bauhaus-black border-3 border-bauhaus-yellow rounded-3xl p-6 md:p-8 flex items-center relative overflow-hidden">
                {/* Background grid */}
                <div className="absolute inset-0 opacity-40">
                  <AnimatedSoftGrid className="w-full h-full text-black" opacity="default" animationPreset="pulse" animationIndex={50} />
                </div>

                {/* Content */}
                <div className="relative z-10 flex-1">
                  <div className="inline-block bg-bauhaus-black text-bauhaus-yellow px-3 py-1 rounded-full text-xs font-bold mb-4">
                    MODULAR
                  </div>
                  <h3 className="text-xl md:text-2xl font-bold mb-4">Custom Gutenberg blocks</h3>
                  <p className="text-gray-800 leading-relaxed max-w-md">
                    Purpose-built content blocks. No extra plugins, no bloat. Just exactly what you need.
                  </p>
                </div>

                {/* Block stack visualization */}
                <div className="relative ml-8 hidden md:block">
                  <div className="space-y-3">
                    <div className="flex items-center space-x-2">
                      <AnimatedSoftCircle size="sm" color="black" className="w-4 h-4" animationPreset="pulse" animationIndex={51} />
                      <AnimatedRoundedRectangle width="xl" height="sm" color="black" className="w-24 h-4" animationPreset="gentle" animationIndex={52} />
                    </div>
                    <div className="flex items-center space-x-2">
                      <AnimatedSoftCircle size="sm" color="black" className="w-4 h-4" animationPreset="pulse" animationIndex={53} />
                      <AnimatedRoundedRectangle width="lg" height="sm" color="black" className="w-20 h-4" animationPreset="gentle" animationIndex={54} />
                    </div>
                    <div className="flex items-center space-x-2">
                      <AnimatedSoftCircle size="sm" color="black" className="w-4 h-4" animationPreset="pulse" animationIndex={55} />
                      <AnimatedRoundedRectangle width="md" height="sm" color="black" className="w-16 h-4" animationPreset="gentle" animationIndex={56} />
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Square Card - API Apps */}
            <div className="col-span-12 md:col-span-4 relative">
              <div className="h-full bg-brand-background border-3 border-brand-background rounded-3xl p-6 md:p-8 flex flex-col justify-center relative overflow-hidden">
                {/* Background grid */}
                <div className="absolute inset-0 opacity-40">
                  <AnimatedSoftGrid className="w-full h-full text-black" opacity="default" animationPreset="energetic" animationIndex={57} />
                </div>

                {/* Content */}
                <div className="relative z-10 text-center">
                  <div className="inline-block bg-bauhaus-red text-white px-3 py-1 rounded-full text-xs font-bold mb-4">
                    CONNECTED
                  </div>
                  <h3 className="text-lg md:text-xl font-bold mb-4 text-bauhaus-black">API-connected web apps</h3>
                  <p className="text-gray-700 leading-relaxed text-sm">
                    Lightweight, maintainable, and fast as hell.
                  </p>
                </div>

                {/* Connection visualization */}
                <div className="absolute inset-0 flex items-center justify-center opacity-60">
                  <div className="relative">
                    <AnimatedSoftCircle size="lg" color="red" className="w-12 h-12" animationPreset="pulse" animationIndex={58} />
                    <div className="absolute -top-6 -left-6">
                      <AnimatedRoundedRectangle width="sm" height="sm" color="blue" className="w-4 h-4" animationPreset="flowing" animationIndex={59} />
                    </div>
                    <div className="absolute -top-6 -right-6">
                      <AnimatedRoundedRectangle width="sm" height="sm" color="yellow" className="w-4 h-4" animationPreset="flowing" animationIndex={60} />
                    </div>
                    <div className="absolute -bottom-6 -left-6">
                      <AnimatedRoundedRectangle width="sm" height="sm" color="blue" className="w-4 h-4" animationPreset="flowing" animationIndex={61} />
                    </div>
                    <div className="absolute -bottom-6 -right-6">
                      <AnimatedRoundedRectangle width="sm" height="sm" color="yellow" className="w-4 h-4" animationPreset="flowing" animationIndex={62} />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Floating background elements */}
        <div className="absolute top-8 left-8 opacity-30">
          <AnimatedSoftCircle size="xl" color="blue" className="w-20 h-20" animationPreset="drift" animationIndex={63} />
        </div>
        <div className="absolute bottom-8 right-8 opacity-30">
          <AnimatedTriangle size="xl" color="red" direction="up" className="w-16 h-16" animationPreset="float" animationIndex={64} />
        </div>
        <div className="absolute top-1/2 right-4 opacity-20">
          <AnimatedRoundedRectangle width="lg" height="xl" color="yellow" className="w-8 h-24" animationPreset="gentle" animationIndex={65} />
        </div>
        <div className="absolute top-1/4 left-1/4 opacity-20">
          <AnimatedSoftCircle size="lg" color="yellow" className="w-16 h-16" animationPreset="float" animationIndex={66} />
        </div>
        <div className="absolute bottom-1/4 left-8 opacity-25">
          <AnimatedRoundedRectangle width="md" height="lg" color="blue" className="w-6 h-16" animationPreset="drift" animationIndex={67} />
        </div>
      </section>

      {/* Who We're For Section */}
      <section className="relative px-6 md:px-12 lg:px-24 py-16 md:py-24 bg-brand-background overflow-hidden">
        <div className="max-w-7xl mx-auto relative z-10">
          <div className="text-center mb-16">
            <h2 className="text-display font-bold mb-8">Who We're For</h2>
            <p className="text-xl text-gray-700 leading-relaxed max-w-3xl mx-auto">
              We work with founders, marketers, and teams who value clarity over chaos.
            </p>
          </div>

          {/* Persona Cards in Diagonal Layout */}
          <div className="relative">
            {/* Founder Persona */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center mb-20">
              <div className="relative h-80 w-full">
                {/* Background grid */}
                <div className="absolute inset-0 opacity-60">
                  <AnimatedSoftGrid className="w-full h-full text-black" opacity="hero" animationPreset="subtle" animationIndex={68} />
                </div>

                {/* Founder avatar composition - dynamic, leadership-focused */}
                <div className="relative flex justify-center items-center h-full">
                  {/* Main avatar shape */}
                  <div className="relative">
                    <AnimatedSoftCircle size="xl" color="red" className="w-24 h-24" animationPreset="gentle" animationIndex={69} />
                    {/* Vision rays */}
                    <div className="absolute -top-8 left-1/2 transform -translate-x-1/2">
                      <AnimatedTriangle size="md" color="yellow" direction="up" className="w-8 h-8" animationPreset="energetic" animationIndex={70} />
                    </div>
                    <div className="absolute -top-4 -left-8">
                      <AnimatedTriangle size="sm" color="yellow" direction="up" className="w-6 h-6" animationPreset="dynamic" animationIndex={71} />
                    </div>
                    <div className="absolute -top-4 -right-8">
                      <AnimatedTriangle size="sm" color="yellow" direction="up" className="w-6 h-6" animationPreset="dynamic" animationIndex={72} />
                    </div>
                  </div>

                  {/* Surrounding elements representing team/network */}
                  <div className="absolute top-8 left-8">
                    <AnimatedSoftCircle size="md" color="blue" className="w-8 h-8" animationPreset="float" animationIndex={73} />
                  </div>
                  <div className="absolute top-8 right-8">
                    <AnimatedSoftCircle size="md" color="blue" className="w-8 h-8" animationPreset="float" animationIndex={74} />
                  </div>
                  <div className="absolute bottom-8 left-12">
                    <AnimatedSoftCircle size="sm" color="blue" className="w-6 h-6" animationPreset="drift" animationIndex={75} />
                  </div>
                  <div className="absolute bottom-8 right-12">
                    <AnimatedSoftCircle size="sm" color="blue" className="w-6 h-6" animationPreset="drift" animationIndex={76} />
                  </div>

                  {/* Growth/progress indicators */}
                  <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2">
                    <div className="flex space-x-1">
                      <AnimatedRoundedRectangle width="sm" height="md" color="red" className="w-2 h-6" animationPreset="pulse" animationIndex={77} />
                      <AnimatedRoundedRectangle width="sm" height="lg" color="red" className="w-2 h-8" animationPreset="pulse" animationIndex={78} />
                      <AnimatedRoundedRectangle width="sm" height="xl" color="red" className="w-2 h-10" animationPreset="pulse" animationIndex={79} />
                    </div>
                  </div>
                </div>
              </div>

              <div className="space-y-6">
                <div className="inline-block bg-bauhaus-red text-white px-4 py-2 rounded-full text-sm font-bold">
                  THE FOUNDER
                </div>
                <h3 className="text-2xl md:text-3xl font-bold">Building something that matters</h3>
                <div className="space-y-4 text-lg text-gray-700">
                  <p className="flex items-center">
                    <AnimatedSoftCircle size="sm" color="red" className="w-3 h-3 mr-3 flex-shrink-0" animationPreset="gentle" animationIndex={80} />
                    Smaller teams, fewer meetings
                  </p>
                  <p className="flex items-center">
                    <AnimatedSoftCircle size="sm" color="red" className="w-3 h-3 mr-3 flex-shrink-0" animationPreset="gentle" animationIndex={81} />
                    Clear vision, faster execution
                  </p>
                  <p className="flex items-center">
                    <AnimatedSoftCircle size="sm" color="red" className="w-3 h-3 mr-3 flex-shrink-0" animationPreset="gentle" animationIndex={82} />
                    Something real, built to last
                  </p>
                </div>
              </div>
            </div>

            {/* Marketer Persona - Right aligned */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center mb-20">
              <div className="space-y-6 lg:order-2">
                <div className="inline-block bg-bauhaus-blue text-white px-4 py-2 rounded-full text-sm font-bold">
                  THE MARKETER
                </div>
                <h3 className="text-2xl md:text-3xl font-bold">Performance that converts</h3>
                <div className="space-y-4 text-lg text-gray-700">
                  <p className="flex items-center">
                    <AnimatedSoftCircle size="sm" color="blue" className="w-3 h-3 mr-3 flex-shrink-0" animationPreset="gentle" animationIndex={83} />
                    Data-driven design decisions
                  </p>
                  <p className="flex items-center">
                    <AnimatedSoftCircle size="sm" color="blue" className="w-3 h-3 mr-3 flex-shrink-0" animationPreset="gentle" animationIndex={84} />
                    Speed that doesn't sacrifice quality
                  </p>
                  <p className="flex items-center">
                    <AnimatedSoftCircle size="sm" color="blue" className="w-3 h-3 mr-3 flex-shrink-0" animationPreset="gentle" animationIndex={85} />
                    Results you can measure
                  </p>
                </div>
              </div>

              <div className="relative h-80 w-full lg:order-1">
                {/* Background grid */}
                <div className="absolute inset-0 opacity-60">
                  <AnimatedSoftGrid className="w-full h-full text-black" opacity="hero" animationPreset="drift" animationIndex={86} />
                </div>

                {/* Marketer avatar composition - analytics/performance focused */}
                <div className="relative flex justify-center items-center h-full">
                  {/* Main avatar shape */}
                  <div className="relative">
                    <AnimatedRoundedRectangle width="xl" height="xl" color="blue" className="w-20 h-20" animationPreset="gentle" animationIndex={87} />
                  </div>

                  {/* Analytics/chart elements */}
                  <div className="absolute top-4 left-4">
                    <div className="flex items-end space-x-1">
                      <AnimatedRoundedRectangle width="sm" height="sm" color="yellow" className="w-2 h-3" animationPreset="pulse" animationIndex={88} />
                      <AnimatedRoundedRectangle width="sm" height="md" color="yellow" className="w-2 h-5" animationPreset="pulse" animationIndex={89} />
                      <AnimatedRoundedRectangle width="sm" height="lg" color="yellow" className="w-2 h-7" animationPreset="pulse" animationIndex={90} />
                      <AnimatedRoundedRectangle width="sm" height="xl" color="yellow" className="w-2 h-9" animationPreset="pulse" animationIndex={91} />
                    </div>
                  </div>

                  {/* Conversion funnels */}
                  <div className="absolute top-4 right-4">
                    <div className="space-y-1">
                      <AnimatedRoundedRectangle width="lg" height="sm" color="red" className="w-12 h-2" animationPreset="flowing" animationIndex={92} />
                      <AnimatedRoundedRectangle width="md" height="sm" color="red" className="w-8 h-2" animationPreset="flowing" animationIndex={93} />
                      <AnimatedRoundedRectangle width="sm" height="sm" color="red" className="w-4 h-2" animationPreset="flowing" animationIndex={94} />
                    </div>
                  </div>

                  {/* Target/goal indicators */}
                  <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2">
                    <div className="relative">
                      <AnimatedSoftCircle size="lg" color="red" className="w-12 h-12 opacity-30" animationPreset="pulse" animationIndex={95} />
                      <AnimatedSoftCircle size="md" color="red" className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-8 h-8 opacity-60" animationPreset="pulse" animationIndex={96} />
                      <AnimatedSoftCircle size="sm" color="red" className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-4 h-4" animationPreset="pulse" animationIndex={97} />
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Team Lead Persona */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <div className="relative h-80 w-full">
                {/* Background grid */}
                <div className="absolute inset-0 opacity-60">
                  <AnimatedSoftGrid className="w-full h-full text-black" opacity="hero" animationPreset="energetic" animationIndex={98} />
                </div>

                {/* Team lead avatar composition - collaboration focused */}
                <div className="relative flex justify-center items-center h-full">
                  {/* Central coordination hub */}
                  <div className="relative">
                    <AnimatedTriangle size="xl" color="yellow" direction="up" className="w-16 h-16" animationPreset="gentle" animationIndex={99} />
                  </div>

                  {/* Team member connections */}
                  <div className="absolute top-8 left-8">
                    <AnimatedSoftCircle size="md" color="blue" className="w-8 h-8" animationPreset="drift" animationIndex={100} />
                    <div className="absolute top-1/2 left-full w-8 h-0.5 bg-gray-400 opacity-50"></div>
                  </div>
                  <div className="absolute top-8 right-8">
                    <AnimatedSoftCircle size="md" color="red" className="w-8 h-8" animationPreset="drift" animationIndex={101} />
                    <div className="absolute top-1/2 right-full w-8 h-0.5 bg-gray-400 opacity-50"></div>
                  </div>
                  <div className="absolute bottom-8 left-8">
                    <AnimatedSoftCircle size="md" color="blue" className="w-8 h-8" animationPreset="float" animationIndex={102} />
                    <div className="absolute bottom-1/2 left-full w-8 h-0.5 bg-gray-400 opacity-50 transform rotate-45"></div>
                  </div>
                  <div className="absolute bottom-8 right-8">
                    <AnimatedSoftCircle size="md" color="red" className="w-8 h-8" animationPreset="float" animationIndex={103} />
                    <div className="absolute bottom-1/2 right-full w-8 h-0.5 bg-gray-400 opacity-50 transform -rotate-45"></div>
                  </div>

                  {/* Process flow indicators */}
                  <div className="absolute top-1/2 left-4 transform -translate-y-1/2">
                    <div className="flex flex-col space-y-2">
                      <AnimatedRoundedRectangle width="sm" height="sm" color="yellow" className="w-4 h-4" animationPreset="pulse" animationIndex={104} />
                      <AnimatedRoundedRectangle width="sm" height="sm" color="yellow" className="w-4 h-4" animationPreset="pulse" animationIndex={105} />
                      <AnimatedRoundedRectangle width="sm" height="sm" color="yellow" className="w-4 h-4" animationPreset="pulse" animationIndex={106} />
                    </div>
                  </div>
                </div>
              </div>

              <div className="space-y-6">
                <div className="inline-block bg-bauhaus-yellow text-bauhaus-black px-4 py-2 rounded-full text-sm font-bold">
                  THE TEAM LEAD
                </div>
                <h3 className="text-2xl md:text-3xl font-bold">Clarity in every process</h3>
                <div className="space-y-4 text-lg text-gray-700">
                  <p className="flex items-center">
                    <AnimatedSoftCircle size="sm" color="yellow" className="w-3 h-3 mr-3 flex-shrink-0" animationPreset="gentle" animationIndex={107} />
                    Transparent communication
                  </p>
                  <p className="flex items-center">
                    <AnimatedSoftCircle size="sm" color="yellow" className="w-3 h-3 mr-3 flex-shrink-0" animationPreset="gentle" animationIndex={108} />
                    Clean workflows, clean code
                  </p>
                  <p className="flex items-center">
                    <AnimatedSoftCircle size="sm" color="yellow" className="w-3 h-3 mr-3 flex-shrink-0" animationPreset="gentle" animationIndex={109} />
                    Scalable systems that work
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Bottom CTA */}
          <div className="text-center mt-20 pt-12 border-t border-gray-300">
            <div className="relative inline-block">
              <p className="text-2xl font-bold text-gray-900 mb-4">
                Still new here? That just means we're hungrier to prove it.
              </p>
              {/* Decorative elements around the CTA */}
              <div className="absolute -top-4 -left-8">
                <AnimatedSoftCircle size="sm" color="red" className="w-4 h-4" animationPreset="gentle" animationIndex={110} />
              </div>
              <div className="absolute -top-4 -right-8">
                <AnimatedSoftCircle size="sm" color="blue" className="w-4 h-4" animationPreset="gentle" animationIndex={111} />
              </div>
              <div className="absolute -bottom-4 left-1/2 transform -translate-x-1/2">
                <AnimatedSoftCircle size="sm" color="yellow" className="w-4 h-4" animationPreset="gentle" animationIndex={112} />
              </div>
            </div>
          </div>
        </div>

        {/* Background floating elements */}
        <div className="absolute top-16 right-8 opacity-20">
          <AnimatedRoundedRectangle width="lg" height="xl" color="blue" className="w-6 h-20" animationPreset="drift" animationIndex={113} />
        </div>
        <div className="absolute bottom-16 left-8 opacity-20">
          <AnimatedTriangle size="lg" color="red" direction="up" className="w-12 h-12" animationPreset="float" animationIndex={114} />
        </div>
        <div className="absolute top-1/3 left-4 opacity-15">
          <AnimatedSoftCircle size="xl" color="yellow" className="w-16 h-16" animationPreset="gentle" animationIndex={115} />
        </div>
      </section>

      {/* What We Build With Section */}
      <section className="px-6 md:px-12 lg:px-24 py-16 md:py-24">
        <div className="max-w-7xl mx-auto">
          <h2 className="text-display font-bold text-center mb-8">What We Build With</h2>
          <p className="text-xl text-center text-gray-700 mb-16 max-w-4xl mx-auto">
            We use tools that are fast, modern, and built to scale. Everything is chosen for clarity, performance, and long-term maintainability — no fluff, no filler.
          </p>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
            {/* Core Stack */}
            <div className="space-y-8">
              <h3 className="text-heading font-bold">Core Stack</h3>
              <div className="space-y-6">
                <div>
                  <h4 className="font-bold text-lg mb-2">WordPress (Custom Themes)</h4>
                  <p className="text-gray-700">Built from scratch — clean, minimal, and made to last.</p>
                </div>
                <div>
                  <h4 className="font-bold text-lg mb-2">Sage (Roots stack)</h4>
                  <p className="text-gray-700">A modern workflow for WordPress, using Blade, Composer, and clean structure.</p>
                </div>
                <div>
                  <h4 className="font-bold text-lg mb-2">Blade</h4>
                  <p className="text-gray-700">Elegant templating with clear separation between logic and layout.</p>
                </div>
                <div>
                  <h4 className="font-bold text-lg mb-2">Gutenberg (Custom Blocks)</h4>
                  <p className="text-gray-700">Native block development with PHP and JavaScript — no ACF Pro.</p>
                </div>
                <div>
                  <h4 className="font-bold text-lg mb-2">PHP</h4>
                  <p className="text-gray-700">Lightweight and structured backend logic.</p>
                </div>
                <div>
                  <h4 className="font-bold text-lg mb-2">JavaScript (Vanilla)</h4>
                  <p className="text-gray-700">Used for interactions and custom block logic — nothing unnecessary.</p>
                </div>
                <div>
                  <h4 className="font-bold text-lg mb-2">Tailwind CSS</h4>
                  <p className="text-gray-700">Utility-first styling that keeps everything lean and consistent.</p>
                </div>
              </div>
            </div>

            {/* Modern Frontend */}
            <div className="space-y-8">
              <h3 className="text-heading font-bold">Modern Frontend</h3>
              <div className="space-y-6">
                <div>
                  <h4 className="font-bold text-lg mb-2">React (when needed)</h4>
                  <p className="text-gray-700">For rich UIs, headless builds, or interactive components — used with intent.</p>
                </div>
                <div>
                  <h4 className="font-bold text-lg mb-2">Headless WordPress</h4>
                  <p className="text-gray-700">REST-based content delivery with frameworks like Next.js or custom React frontends.</p>
                </div>
                <div>
                  <h4 className="font-bold text-lg mb-2">Alpine.js</h4>
                  <p className="text-gray-700">When reactivity's needed but React would be overkill.</p>
                </div>
                <div>
                  <h4 className="font-bold text-lg mb-2">Vite + ESBuild</h4>
                  <p className="text-gray-700">Fast bundling, instant reloads, and smooth local development.</p>
                </div>
              </div>

              <h3 className="text-heading font-bold pt-8">Tooling & Workflow</h3>
              <div className="space-y-6">
                <div>
                  <h4 className="font-bold text-lg mb-2">Composer / NPM / Yarn</h4>
                  <p className="text-gray-700">Proper package management — backend and frontend.</p>
                </div>
                <div>
                  <h4 className="font-bold text-lg mb-2">PostCSS</h4>
                  <p className="text-gray-700">Tailwind config, purging, prefixing, and build tweaks.</p>
                </div>
                <div>
                  <h4 className="font-bold text-lg mb-2">Git + GitHub</h4>
                  <p className="text-gray-700">Everything versioned, reviewed, and documented.</p>
                </div>
                <div>
                  <h4 className="font-bold text-lg mb-2">Figma</h4>
                  <p className="text-gray-700">Used for design systems, wireframes, and handoff.</p>
                </div>
                <div>
                  <h4 className="font-bold text-lg mb-2">Markdown + Docs</h4>
                  <p className="text-gray-700">Clean documentation for devs and clients.</p>
                </div>
              </div>
            </div>

            {/* Extended Capabilities */}
            <div className="space-y-8">
              <h3 className="text-heading font-bold">Extended Capabilities</h3>
              <div className="space-y-6">
                <div>
                  <h4 className="font-bold text-lg mb-2">Next.js</h4>
                  <p className="text-gray-700">Modern React frontend when needed</p>
                </div>
                <div>
                  <h4 className="font-bold text-lg mb-2">MySQL</h4>
                  <p className="text-gray-700">Standard WordPress data layer</p>
                </div>
                <div>
                  <h4 className="font-bold text-lg mb-2">DigitalOcean / Vercel / Netlify</h4>
                  <p className="text-gray-700">Deployment options tailored to the stack</p>
                </div>
                <div>
                  <h4 className="font-bold text-lg mb-2">REST APIs</h4>
                  <p className="text-gray-700">For custom integrations, data, or decoupled setups</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Final CTA Section */}
      <section className="px-6 md:px-12 lg:px-24 py-24 md:py-32 bg-bauhaus-black text-bauhaus-white relative overflow-hidden">
        <div className="max-w-4xl mx-auto text-center relative z-10">
          <h2 className="text-display font-bold mb-6">Got something worth building?</h2>
          <p className="text-xl mb-12 leading-relaxed">
            Let's make it real. We'll help you strip it down to what matters — and bring it to life.
          </p>
          <Link href="/contact" className="btn-primary bg-brand-background text-bauhaus-black border-brand-background hover:bg-transparent hover:text-brand-background inline-block">
            Start Your Project
          </Link>
        </div>

        {/* Geometric motif with grids */}
        <div className="absolute inset-0 opacity-10">
          <OrganicComposition variant="feature" className="w-full h-full" />
        </div>
        <div className="absolute top-8 left-8 opacity-60">
          <div className="relative">
            <AnimatedSoftGrid className="w-32 h-32 text-black" animationPreset="drift" animationIndex={41} />
            <AnimatedSoftCircle size="xl" color="red" className="absolute top-4 left-4 w-20 h-20" animationPreset="energetic" animationIndex={42} />
          </div>
        </div>
        <div className="absolute bottom-8 right-8 opacity-60">
          <div className="relative">
            <AnimatedSoftGrid className="w-28 h-28 text-black" animationPreset="float" animationIndex={43} />
            <AnimatedRoundedRectangle width="xl" height="lg" color="yellow" className="absolute top-3 left-3 w-18 h-14" animationPreset="flowing" animationIndex={44} />
          </div>
        </div>
      </section>
    </PageWrapper>
  )
}
