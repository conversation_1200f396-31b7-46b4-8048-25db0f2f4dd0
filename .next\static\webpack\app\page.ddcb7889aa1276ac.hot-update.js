"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/shapes/RoundedShapes.tsx":
/*!*************************************************!*\
  !*** ./src/components/shapes/RoundedShapes.tsx ***!
  \*************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Blob: function() { return /* binding */ Blob; },\n/* harmony export */   HalfCircle: function() { return /* binding */ HalfCircle; },\n/* harmony export */   Pill: function() { return /* binding */ Pill; },\n/* harmony export */   QuarterCircle: function() { return /* binding */ QuarterCircle; },\n/* harmony export */   RoundedRect: function() { return /* binding */ RoundedRect; },\n/* harmony export */   RoundedSquareWithCutout: function() { return /* binding */ RoundedSquareWithCutout; },\n/* harmony export */   SoftGrid: function() { return /* binding */ SoftGrid; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n\n// Rounded Rectangle with soft corners\nfunction RoundedRect(param) {\n    let { className = \"\", color = \"blue\" } = param;\n    const colorClasses = {\n        red: \"bg-brand-red\",\n        yellow: \"bg-brand-yellow\",\n        blue: \"bg-brand-blue\",\n        black: \"bg-black\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"\".concat(colorClasses[color], \" rounded-3xl \").concat(className)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\RoundedShapes.tsx\",\n        lineNumber: 16,\n        columnNumber: 5\n    }, this);\n}\n_c = RoundedRect;\n// Pill shape (very rounded rectangle)\nfunction Pill(param) {\n    let { className = \"\", color = \"yellow\" } = param;\n    const colorClasses = {\n        red: \"bg-brand-red\",\n        yellow: \"bg-brand-yellow\",\n        blue: \"bg-brand-blue\",\n        black: \"bg-black\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"\".concat(colorClasses[color], \" rounded-full \").concat(className)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\RoundedShapes.tsx\",\n        lineNumber: 30,\n        columnNumber: 5\n    }, this);\n}\n_c1 = Pill;\n// Organic blob shape using CSS\nfunction Blob(param) {\n    let { className = \"\", color = \"red\" } = param;\n    const colorClasses = {\n        red: \"bg-brand-red\",\n        yellow: \"bg-brand-yellow\",\n        blue: \"bg-brand-blue\",\n        black: \"bg-black\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"\".concat(colorClasses[color], \" \").concat(className),\n        style: {\n            borderRadius: \"60% 40% 30% 70% / 60% 30% 70% 40%\"\n        }\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\RoundedShapes.tsx\",\n        lineNumber: 44,\n        columnNumber: 5\n    }, this);\n}\n_c2 = Blob;\n// Half circle (like in your references)\nfunction HalfCircle(param) {\n    let { className = \"\", color = \"blue\", direction = \"right\" } = param;\n    const colorClasses = {\n        red: \"bg-brand-red\",\n        yellow: \"bg-brand-yellow\",\n        blue: \"bg-brand-blue\",\n        black: \"bg-black\"\n    };\n    const directionClasses = {\n        right: \"rounded-l-full\",\n        left: \"rounded-r-full\",\n        top: \"rounded-b-full\",\n        bottom: \"rounded-t-full\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"\".concat(colorClasses[color], \" \").concat(directionClasses[direction], \" \").concat(className)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\RoundedShapes.tsx\",\n        lineNumber: 70,\n        columnNumber: 5\n    }, this);\n}\n_c3 = HalfCircle;\n// Quarter circle\nfunction QuarterCircle(param) {\n    let { className = \"\", color = \"yellow\", corner = \"top-left\" } = param;\n    const colorClasses = {\n        red: \"bg-brand-red\",\n        yellow: \"bg-brand-yellow\",\n        blue: \"bg-brand-blue\",\n        black: \"bg-black\"\n    };\n    const cornerClasses = {\n        \"top-left\": \"rounded-br-full\",\n        \"top-right\": \"rounded-bl-full\",\n        \"bottom-left\": \"rounded-tr-full\",\n        \"bottom-right\": \"rounded-tl-full\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"\".concat(colorClasses[color], \" \").concat(cornerClasses[corner], \" \").concat(className)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\RoundedShapes.tsx\",\n        lineNumber: 91,\n        columnNumber: 5\n    }, this);\n}\n_c4 = QuarterCircle;\n// Rounded square with cutout (inspired by your logo)\nfunction RoundedSquareWithCutout(param) {\n    let { className = \"\", color = \"black\" } = param;\n    const colorClasses = {\n        red: \"bg-brand-red\",\n        yellow: \"bg-brand-yellow\",\n        blue: \"bg-brand-blue\",\n        black: \"bg-black\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"\".concat(colorClasses[color], \" rounded-3xl w-full h-full\")\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\RoundedShapes.tsx\",\n                lineNumber: 106,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-brand-background rounded-full w-1/2 h-1/2\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\RoundedShapes.tsx\",\n                lineNumber: 107,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\RoundedShapes.tsx\",\n        lineNumber: 105,\n        columnNumber: 5\n    }, this);\n}\n_c5 = RoundedSquareWithCutout;\n// Soft grid overlay (like in the DSM reference)\nfunction SoftGrid(param) {\n    let { className = \"\" } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"absolute inset-0 opacity-40 \".concat(className),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            width: \"100%\",\n            height: \"100%\",\n            className: \"w-full h-full\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"defs\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pattern\", {\n                        id: \"grid\",\n                        width: \"40\",\n                        height: \"40\",\n                        patternUnits: \"userSpaceOnUse\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            d: \"M 40 0 L 0 0 0 40\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            strokeWidth: \"1\",\n                            opacity: \"0.6\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\RoundedShapes.tsx\",\n                            lineNumber: 119,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\RoundedShapes.tsx\",\n                        lineNumber: 118,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\RoundedShapes.tsx\",\n                    lineNumber: 117,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                    width: \"100%\",\n                    height: \"100%\",\n                    fill: \"url(#grid)\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\RoundedShapes.tsx\",\n                    lineNumber: 122,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\RoundedShapes.tsx\",\n            lineNumber: 116,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\RoundedShapes.tsx\",\n        lineNumber: 115,\n        columnNumber: 5\n    }, this);\n}\n_c6 = SoftGrid;\nvar _c, _c1, _c2, _c3, _c4, _c5, _c6;\n$RefreshReg$(_c, \"RoundedRect\");\n$RefreshReg$(_c1, \"Pill\");\n$RefreshReg$(_c2, \"Blob\");\n$RefreshReg$(_c3, \"HalfCircle\");\n$RefreshReg$(_c4, \"QuarterCircle\");\n$RefreshReg$(_c5, \"RoundedSquareWithCutout\");\n$RefreshReg$(_c6, \"SoftGrid\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/shapes/RoundedShapes.tsx\n"));

/***/ })

});